// Code generated by protoc-gen-go. DO NOT EDIT.
// source: base.proto

package mmproto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SKBuiltinBuffert struct {
	ILen                 *uint32  `protobuf:"varint,1,req,name=iLen" json:"iLen,omitempty"`
	Buffer               []byte   `protobuf:"bytes,2,opt,name=Buffer" json:"Buffer,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SKBuiltinBuffert) Reset()         { *m = SKBuiltinBuffert{} }
func (m *SKBuiltinBuffert) String() string { return proto.CompactTextString(m) }
func (*SKBuiltinBuffert) ProtoMessage()    {}
func (*SKBuiltinBuffert) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{0}
}

func (m *SKBuiltinBuffert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SKBuiltinBuffert.Unmarshal(m, b)
}
func (m *SKBuiltinBuffert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SKBuiltinBuffert.Marshal(b, m, deterministic)
}
func (m *SKBuiltinBuffert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SKBuiltinBuffert.Merge(m, src)
}
func (m *SKBuiltinBuffert) XXX_Size() int {
	return xxx_messageInfo_SKBuiltinBuffert.Size(m)
}
func (m *SKBuiltinBuffert) XXX_DiscardUnknown() {
	xxx_messageInfo_SKBuiltinBuffert.DiscardUnknown(m)
}

var xxx_messageInfo_SKBuiltinBuffert proto.InternalMessageInfo

func (m *SKBuiltinBuffert) GetILen() uint32 {
	if m != nil && m.ILen != nil {
		return *m.ILen
	}
	return 0
}

func (m *SKBuiltinBuffert) GetBuffer() []byte {
	if m != nil {
		return m.Buffer
	}
	return nil
}

type SKBuiltinStringt struct {
	String_              *string  `protobuf:"bytes,1,opt,name=String" json:"String,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SKBuiltinStringt) Reset()         { *m = SKBuiltinStringt{} }
func (m *SKBuiltinStringt) String() string { return proto.CompactTextString(m) }
func (*SKBuiltinStringt) ProtoMessage()    {}
func (*SKBuiltinStringt) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{1}
}

func (m *SKBuiltinStringt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SKBuiltinStringt.Unmarshal(m, b)
}
func (m *SKBuiltinStringt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SKBuiltinStringt.Marshal(b, m, deterministic)
}
func (m *SKBuiltinStringt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SKBuiltinStringt.Merge(m, src)
}
func (m *SKBuiltinStringt) XXX_Size() int {
	return xxx_messageInfo_SKBuiltinStringt.Size(m)
}
func (m *SKBuiltinStringt) XXX_DiscardUnknown() {
	xxx_messageInfo_SKBuiltinStringt.DiscardUnknown(m)
}

var xxx_messageInfo_SKBuiltinStringt proto.InternalMessageInfo

func (m *SKBuiltinStringt) GetString_() string {
	if m != nil && m.String_ != nil {
		return *m.String_
	}
	return ""
}

type BaseRequest struct {
	SessionKey           []byte   `protobuf:"bytes,1,req,name=SessionKey" json:"SessionKey,omitempty"`
	Uin                  *uint64  `protobuf:"varint,2,req,name=Uin" json:"Uin,omitempty"`
	DeviceID             []byte   `protobuf:"bytes,3,req,name=DeviceID" json:"DeviceID,omitempty"`
	ClientVersion        *int32   `protobuf:"varint,4,req,name=ClientVersion" json:"ClientVersion,omitempty"`
	DeviceType           *string  `protobuf:"bytes,5,req,name=DeviceType" json:"DeviceType,omitempty"`
	Scene                *uint32  `protobuf:"varint,6,req,name=Scene" json:"Scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{2}
}

func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (m *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(m, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetSessionKey() []byte {
	if m != nil {
		return m.SessionKey
	}
	return nil
}

func (m *BaseRequest) GetUin() uint64 {
	if m != nil && m.Uin != nil {
		return *m.Uin
	}
	return 0
}

func (m *BaseRequest) GetDeviceID() []byte {
	if m != nil {
		return m.DeviceID
	}
	return nil
}

func (m *BaseRequest) GetClientVersion() int32 {
	if m != nil && m.ClientVersion != nil {
		return *m.ClientVersion
	}
	return 0
}

func (m *BaseRequest) GetDeviceType() string {
	if m != nil && m.DeviceType != nil {
		return *m.DeviceType
	}
	return ""
}

func (m *BaseRequest) GetScene() uint32 {
	if m != nil && m.Scene != nil {
		return *m.Scene
	}
	return 0
}

type BaseResponse struct {
	Ret                  *int32            `protobuf:"varint,1,req,name=Ret" json:"Ret,omitempty"`
	ErrMsg               *SKBuiltinStringt `protobuf:"bytes,2,req,name=ErrMsg" json:"ErrMsg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BaseResponse) Reset()         { *m = BaseResponse{} }
func (m *BaseResponse) String() string { return proto.CompactTextString(m) }
func (*BaseResponse) ProtoMessage()    {}
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{3}
}

func (m *BaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseResponse.Unmarshal(m, b)
}
func (m *BaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseResponse.Marshal(b, m, deterministic)
}
func (m *BaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseResponse.Merge(m, src)
}
func (m *BaseResponse) XXX_Size() int {
	return xxx_messageInfo_BaseResponse.Size(m)
}
func (m *BaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BaseResponse proto.InternalMessageInfo

func (m *BaseResponse) GetRet() int32 {
	if m != nil && m.Ret != nil {
		return *m.Ret
	}
	return 0
}

func (m *BaseResponse) GetErrMsg() *SKBuiltinStringt {
	if m != nil {
		return m.ErrMsg
	}
	return nil
}

type ECDHKey struct {
	Nid                  *int32            `protobuf:"varint,1,req,name=Nid" json:"Nid,omitempty"`
	Key                  *SKBuiltinBuffert `protobuf:"bytes,2,req,name=Key" json:"Key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ECDHKey) Reset()         { *m = ECDHKey{} }
func (m *ECDHKey) String() string { return proto.CompactTextString(m) }
func (*ECDHKey) ProtoMessage()    {}
func (*ECDHKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{4}
}

func (m *ECDHKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ECDHKey.Unmarshal(m, b)
}
func (m *ECDHKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ECDHKey.Marshal(b, m, deterministic)
}
func (m *ECDHKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ECDHKey.Merge(m, src)
}
func (m *ECDHKey) XXX_Size() int {
	return xxx_messageInfo_ECDHKey.Size(m)
}
func (m *ECDHKey) XXX_DiscardUnknown() {
	xxx_messageInfo_ECDHKey.DiscardUnknown(m)
}

var xxx_messageInfo_ECDHKey proto.InternalMessageInfo

func (m *ECDHKey) GetNid() int32 {
	if m != nil && m.Nid != nil {
		return *m.Nid
	}
	return 0
}

func (m *ECDHKey) GetKey() *SKBuiltinBuffert {
	if m != nil {
		return m.Key
	}
	return nil
}

type ZTData struct {
	Version              *string  `protobuf:"bytes,1,req,name=version" json:"version,omitempty"`
	Encrypted            *uint32  `protobuf:"varint,2,req,name=encrypted" json:"encrypted,omitempty"`
	Data                 []byte   `protobuf:"bytes,3,req,name=data" json:"data,omitempty"`
	TimeStamp            *uint32  `protobuf:"varint,4,req,name=timeStamp" json:"timeStamp,omitempty"`
	Optype               *uint32  `protobuf:"varint,5,req,name=optype" json:"optype,omitempty"`
	Uin                  *uint32  `protobuf:"varint,6,req,name=uin" json:"uin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZTData) Reset()         { *m = ZTData{} }
func (m *ZTData) String() string { return proto.CompactTextString(m) }
func (*ZTData) ProtoMessage()    {}
func (*ZTData) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{5}
}

func (m *ZTData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZTData.Unmarshal(m, b)
}
func (m *ZTData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZTData.Marshal(b, m, deterministic)
}
func (m *ZTData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZTData.Merge(m, src)
}
func (m *ZTData) XXX_Size() int {
	return xxx_messageInfo_ZTData.Size(m)
}
func (m *ZTData) XXX_DiscardUnknown() {
	xxx_messageInfo_ZTData.DiscardUnknown(m)
}

var xxx_messageInfo_ZTData proto.InternalMessageInfo

func (m *ZTData) GetVersion() string {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return ""
}

func (m *ZTData) GetEncrypted() uint32 {
	if m != nil && m.Encrypted != nil {
		return *m.Encrypted
	}
	return 0
}

func (m *ZTData) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ZTData) GetTimeStamp() uint32 {
	if m != nil && m.TimeStamp != nil {
		return *m.TimeStamp
	}
	return 0
}

func (m *ZTData) GetOptype() uint32 {
	if m != nil && m.Optype != nil {
		return *m.Optype
	}
	return 0
}

func (m *ZTData) GetUin() uint32 {
	if m != nil && m.Uin != nil {
		return *m.Uin
	}
	return 0
}

type DeviceToken struct {
	Version              *string           `protobuf:"bytes,1,req,name=version" json:"version,omitempty"`
	Encrypted            *uint32           `protobuf:"varint,2,req,name=encrypted" json:"encrypted,omitempty"`
	Data                 *SKBuiltinStringt `protobuf:"bytes,3,req,name=data" json:"data,omitempty"`
	TimeStamp            *uint32           `protobuf:"varint,4,req,name=timeStamp" json:"timeStamp,omitempty"`
	Optype               *uint32           `protobuf:"varint,5,req,name=optype" json:"optype,omitempty"`
	Uin                  *uint32           `protobuf:"varint,6,req,name=uin" json:"uin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DeviceToken) Reset()         { *m = DeviceToken{} }
func (m *DeviceToken) String() string { return proto.CompactTextString(m) }
func (*DeviceToken) ProtoMessage()    {}
func (*DeviceToken) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{6}
}

func (m *DeviceToken) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceToken.Unmarshal(m, b)
}
func (m *DeviceToken) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceToken.Marshal(b, m, deterministic)
}
func (m *DeviceToken) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceToken.Merge(m, src)
}
func (m *DeviceToken) XXX_Size() int {
	return xxx_messageInfo_DeviceToken.Size(m)
}
func (m *DeviceToken) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceToken.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceToken proto.InternalMessageInfo

func (m *DeviceToken) GetVersion() string {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return ""
}

func (m *DeviceToken) GetEncrypted() uint32 {
	if m != nil && m.Encrypted != nil {
		return *m.Encrypted
	}
	return 0
}

func (m *DeviceToken) GetData() *SKBuiltinStringt {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DeviceToken) GetTimeStamp() uint32 {
	if m != nil && m.TimeStamp != nil {
		return *m.TimeStamp
	}
	return 0
}

func (m *DeviceToken) GetOptype() uint32 {
	if m != nil && m.Optype != nil {
		return *m.Optype
	}
	return 0
}

func (m *DeviceToken) GetUin() uint32 {
	if m != nil && m.Uin != nil {
		return *m.Uin
	}
	return 0
}

type SpamDataSubBody struct {
	Ilen                 *uint32  `protobuf:"varint,1,req,name=ilen" json:"ilen,omitempty"`
	Ztdata               *ZTData  `protobuf:"bytes,2,req,name=ztdata" json:"ztdata,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SpamDataSubBody) Reset()         { *m = SpamDataSubBody{} }
func (m *SpamDataSubBody) String() string { return proto.CompactTextString(m) }
func (*SpamDataSubBody) ProtoMessage()    {}
func (*SpamDataSubBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{7}
}

func (m *SpamDataSubBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpamDataSubBody.Unmarshal(m, b)
}
func (m *SpamDataSubBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpamDataSubBody.Marshal(b, m, deterministic)
}
func (m *SpamDataSubBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpamDataSubBody.Merge(m, src)
}
func (m *SpamDataSubBody) XXX_Size() int {
	return xxx_messageInfo_SpamDataSubBody.Size(m)
}
func (m *SpamDataSubBody) XXX_DiscardUnknown() {
	xxx_messageInfo_SpamDataSubBody.DiscardUnknown(m)
}

var xxx_messageInfo_SpamDataSubBody proto.InternalMessageInfo

func (m *SpamDataSubBody) GetIlen() uint32 {
	if m != nil && m.Ilen != nil {
		return *m.Ilen
	}
	return 0
}

func (m *SpamDataSubBody) GetZtdata() *ZTData {
	if m != nil {
		return m.Ztdata
	}
	return nil
}

type DeviceTokenBody struct {
	Ilen                 *uint32      `protobuf:"varint,1,req,name=ilen" json:"ilen,omitempty"`
	DeviceToken          *DeviceToken `protobuf:"bytes,2,req,name=deviceToken" json:"deviceToken,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DeviceTokenBody) Reset()         { *m = DeviceTokenBody{} }
func (m *DeviceTokenBody) String() string { return proto.CompactTextString(m) }
func (*DeviceTokenBody) ProtoMessage()    {}
func (*DeviceTokenBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{8}
}

func (m *DeviceTokenBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceTokenBody.Unmarshal(m, b)
}
func (m *DeviceTokenBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceTokenBody.Marshal(b, m, deterministic)
}
func (m *DeviceTokenBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceTokenBody.Merge(m, src)
}
func (m *DeviceTokenBody) XXX_Size() int {
	return xxx_messageInfo_DeviceTokenBody.Size(m)
}
func (m *DeviceTokenBody) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceTokenBody.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceTokenBody proto.InternalMessageInfo

func (m *DeviceTokenBody) GetIlen() uint32 {
	if m != nil && m.Ilen != nil {
		return *m.Ilen
	}
	return 0
}

func (m *DeviceTokenBody) GetDeviceToken() *DeviceToken {
	if m != nil {
		return m.DeviceToken
	}
	return nil
}

type SpamDataBody struct {
	Ccd1                 *SpamDataSubBody `protobuf:"bytes,1,opt,name=ccd1" json:"ccd1,omitempty"`
	Ccd2                 *SpamDataSubBody `protobuf:"bytes,2,opt,name=ccd2" json:"ccd2,omitempty"`
	Ccd3                 *SpamDataSubBody `protobuf:"bytes,3,opt,name=ccd3" json:"ccd3,omitempty"`
	Dt                   *DeviceTokenBody `protobuf:"bytes,7,opt,name=dt" json:"dt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SpamDataBody) Reset()         { *m = SpamDataBody{} }
func (m *SpamDataBody) String() string { return proto.CompactTextString(m) }
func (*SpamDataBody) ProtoMessage()    {}
func (*SpamDataBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{9}
}

func (m *SpamDataBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpamDataBody.Unmarshal(m, b)
}
func (m *SpamDataBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpamDataBody.Marshal(b, m, deterministic)
}
func (m *SpamDataBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpamDataBody.Merge(m, src)
}
func (m *SpamDataBody) XXX_Size() int {
	return xxx_messageInfo_SpamDataBody.Size(m)
}
func (m *SpamDataBody) XXX_DiscardUnknown() {
	xxx_messageInfo_SpamDataBody.DiscardUnknown(m)
}

var xxx_messageInfo_SpamDataBody proto.InternalMessageInfo

func (m *SpamDataBody) GetCcd1() *SpamDataSubBody {
	if m != nil {
		return m.Ccd1
	}
	return nil
}

func (m *SpamDataBody) GetCcd2() *SpamDataSubBody {
	if m != nil {
		return m.Ccd2
	}
	return nil
}

func (m *SpamDataBody) GetCcd3() *SpamDataSubBody {
	if m != nil {
		return m.Ccd3
	}
	return nil
}

func (m *SpamDataBody) GetDt() *DeviceTokenBody {
	if m != nil {
		return m.Dt
	}
	return nil
}

type SpamData struct {
	Totallen             *uint32       `protobuf:"varint,1,req,name=totallen" json:"totallen,omitempty"`
	SpamDataBody         *SpamDataBody `protobuf:"bytes,2,req,name=spamDataBody" json:"spamDataBody,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SpamData) Reset()         { *m = SpamData{} }
func (m *SpamData) String() string { return proto.CompactTextString(m) }
func (*SpamData) ProtoMessage()    {}
func (*SpamData) Descriptor() ([]byte, []int) {
	return fileDescriptor_db1b6b0986796150, []int{10}
}

func (m *SpamData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpamData.Unmarshal(m, b)
}
func (m *SpamData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpamData.Marshal(b, m, deterministic)
}
func (m *SpamData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpamData.Merge(m, src)
}
func (m *SpamData) XXX_Size() int {
	return xxx_messageInfo_SpamData.Size(m)
}
func (m *SpamData) XXX_DiscardUnknown() {
	xxx_messageInfo_SpamData.DiscardUnknown(m)
}

var xxx_messageInfo_SpamData proto.InternalMessageInfo

func (m *SpamData) GetTotallen() uint32 {
	if m != nil && m.Totallen != nil {
		return *m.Totallen
	}
	return 0
}

func (m *SpamData) GetSpamDataBody() *SpamDataBody {
	if m != nil {
		return m.SpamDataBody
	}
	return nil
}

func init() {
	proto.RegisterType((*SKBuiltinBuffert)(nil), "mmproto.SKBuiltinBuffert")
	proto.RegisterType((*SKBuiltinStringt)(nil), "mmproto.SKBuiltinStringt")
	proto.RegisterType((*BaseRequest)(nil), "mmproto.BaseRequest")
	proto.RegisterType((*BaseResponse)(nil), "mmproto.BaseResponse")
	proto.RegisterType((*ECDHKey)(nil), "mmproto.ECDHKey")
	proto.RegisterType((*ZTData)(nil), "mmproto.ZTData")
	proto.RegisterType((*DeviceToken)(nil), "mmproto.DeviceToken")
	proto.RegisterType((*SpamDataSubBody)(nil), "mmproto.SpamDataSubBody")
	proto.RegisterType((*DeviceTokenBody)(nil), "mmproto.DeviceTokenBody")
	proto.RegisterType((*SpamDataBody)(nil), "mmproto.SpamDataBody")
	proto.RegisterType((*SpamData)(nil), "mmproto.SpamData")
}

func init() {
	proto.RegisterFile("base.proto", fileDescriptor_db1b6b0986796150)
}

var fileDescriptor_db1b6b0986796150 = []byte{
	// 554 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x53, 0x5d, 0x8b, 0xd3, 0x40,
	0x14, 0x25, 0xe9, 0xd7, 0xee, 0x6d, 0x4b, 0x97, 0x61, 0x95, 0x51, 0x44, 0xc2, 0x20, 0x18, 0xfc,
	0x28, 0x6c, 0x17, 0x04, 0x5f, 0x7c, 0xe8, 0x76, 0x61, 0x65, 0xb5, 0x0f, 0x93, 0xd5, 0x07, 0xc1,
	0x87, 0xd9, 0xe6, 0xee, 0x32, 0xd8, 0x4e, 0x62, 0x32, 0x59, 0xa8, 0xbf, 0xc5, 0xff, 0xe1, 0x8b,
	0x4f, 0xfe, 0x32, 0x99, 0xc9, 0xa4, 0x49, 0x2b, 0xab, 0x22, 0xbe, 0xdd, 0x7b, 0x73, 0xe6, 0xe4,
	0x9c, 0x73, 0xb9, 0x00, 0x97, 0x22, 0xc7, 0x71, 0x9a, 0x25, 0x3a, 0x21, 0xbd, 0xd5, 0xca, 0x16,
	0xec, 0x15, 0x1c, 0x44, 0xe7, 0xd3, 0x42, 0x2e, 0xb5, 0x54, 0xd3, 0xe2, 0xea, 0x0a, 0x33, 0x4d,
	0x08, 0xb4, 0xe5, 0x1b, 0x54, 0xd4, 0x0b, 0xfc, 0x70, 0xc8, 0x6d, 0x4d, 0xee, 0x42, 0xb7, 0xfc,
	0x4c, 0xfd, 0xc0, 0x0b, 0x07, 0xdc, 0x75, 0xec, 0x49, 0xe3, 0x7d, 0xa4, 0x33, 0xa9, 0xae, 0xb5,
	0xc1, 0x96, 0x25, 0xf5, 0x02, 0x2f, 0xdc, 0xe7, 0xae, 0x63, 0xdf, 0x3c, 0xe8, 0x4f, 0x45, 0x8e,
	0x1c, 0x3f, 0x17, 0x98, 0x6b, 0xf2, 0x10, 0x20, 0xc2, 0x3c, 0x97, 0x89, 0x3a, 0xc7, 0xb5, 0xfd,
	0xdb, 0x80, 0x37, 0x26, 0xe4, 0x00, 0x5a, 0xef, 0xa4, 0xa2, 0x7e, 0xe0, 0x87, 0x6d, 0x6e, 0x4a,
	0x72, 0x1f, 0xf6, 0x66, 0x78, 0x23, 0x17, 0xf8, 0x7a, 0x46, 0x5b, 0x16, 0xbf, 0xe9, 0xc9, 0x23,
	0x18, 0x9e, 0x2c, 0x25, 0x2a, 0xfd, 0x1e, 0x33, 0xc3, 0x40, 0xdb, 0x81, 0x1f, 0x76, 0xf8, 0xf6,
	0xd0, 0xfc, 0xb3, 0x7c, 0x71, 0xb1, 0x4e, 0x91, 0x76, 0x02, 0x3f, 0xdc, 0xe7, 0x8d, 0x09, 0x39,
	0x84, 0x4e, 0xb4, 0x40, 0x85, 0xb4, 0x6b, 0xcd, 0x97, 0x0d, 0x8b, 0x60, 0x50, 0x0a, 0xcf, 0xd3,
	0x44, 0xe5, 0x68, 0x94, 0x71, 0xd4, 0x56, 0x72, 0x87, 0x9b, 0x92, 0x1c, 0x41, 0xf7, 0x34, 0xcb,
	0xde, 0xe6, 0xd7, 0x56, 0x6e, 0x7f, 0x72, 0x6f, 0xec, 0x12, 0x1e, 0xef, 0xc6, 0xc3, 0x1d, 0x90,
	0x9d, 0x41, 0xef, 0xf4, 0x64, 0x76, 0xe6, 0x9c, 0xce, 0x65, 0x5c, 0xf1, 0xcd, 0x65, 0x4c, 0x9e,
	0x42, 0xcb, 0x84, 0x72, 0x2b, 0x99, 0xdb, 0x15, 0x37, 0x28, 0xf6, 0xd5, 0x83, 0xee, 0x87, 0x8b,
	0x99, 0xd0, 0x82, 0x50, 0xe8, 0xdd, 0x38, 0xff, 0x9e, 0x35, 0x57, 0xb5, 0xe4, 0x01, 0xec, 0xa3,
	0x5a, 0x64, 0xeb, 0x54, 0x63, 0x6c, 0x79, 0x87, 0xbc, 0x1e, 0x98, 0x9d, 0xc7, 0x42, 0x0b, 0x97,
	0xaa, 0xad, 0xcd, 0x0b, 0x2d, 0x57, 0x18, 0x69, 0xb1, 0x4a, 0x6d, 0x9a, 0x43, 0x5e, 0x0f, 0xcc,
	0x96, 0x93, 0x54, 0x57, 0x29, 0x0e, 0xb9, 0xeb, 0x8c, 0x97, 0x42, 0x2a, 0x97, 0x9f, 0x29, 0xd9,
	0x77, 0x0f, 0xfa, 0x2e, 0xe2, 0xe4, 0x13, 0xaa, 0x7f, 0xd6, 0xf8, 0xbc, 0xa1, 0xf1, 0xb7, 0x09,
	0xff, 0x5f, 0xf9, 0x73, 0x18, 0x45, 0xa9, 0x58, 0x99, 0x78, 0xa3, 0xe2, 0x72, 0x9a, 0xc4, 0x6b,
	0x7b, 0x21, 0xcb, 0xc6, 0x85, 0x2c, 0x51, 0x91, 0xc7, 0xd0, 0xfd, 0xa2, 0xad, 0xbe, 0x72, 0x69,
	0xa3, 0x8d, 0xbe, 0x72, 0x35, 0xdc, 0x7d, 0x66, 0x1f, 0x61, 0xd4, 0x48, 0xe3, 0x56, 0xbe, 0x17,
	0xd0, 0x8f, 0x6b, 0x98, 0x23, 0x3d, 0xdc, 0x90, 0x36, 0x28, 0x78, 0x13, 0xc8, 0x7e, 0x78, 0x30,
	0xa8, 0xf4, 0x5a, 0xf2, 0x67, 0xd0, 0x5e, 0x2c, 0xe2, 0x23, 0x7b, 0x8c, 0xfd, 0x09, 0xad, 0x63,
	0xdb, 0x36, 0xc5, 0x2d, 0xca, 0xa1, 0x27, 0xf6, 0xcc, 0xff, 0x84, 0x9e, 0x38, 0xf4, 0x31, 0x6d,
	0xfd, 0x05, 0xfa, 0x98, 0x84, 0xe0, 0xc7, 0x9a, 0xf6, 0x76, 0xb0, 0x3b, 0x61, 0x70, 0x3f, 0xd6,
	0x4c, 0xc0, 0x5e, 0x45, 0x61, 0x8e, 0x5e, 0x27, 0x5a, 0x2c, 0xeb, 0x80, 0x36, 0x3d, 0x79, 0x09,
	0x83, 0xbc, 0xe1, 0xd5, 0xa5, 0x74, 0xe7, 0x17, 0x1d, 0x96, 0x78, 0x0b, 0xfa, 0x33, 0x00, 0x00,
	0xff, 0xff, 0x11, 0x3e, 0x7e, 0xb4, 0x0f, 0x05, 0x00, 0x00,
}

package Wxapp

import (
	"encoding/json"
	"fmt"
	"strings"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
)

type Phoneitem struct {
	Mobile         string `json:"mobile"`
	Show_mobile    string `json:"show_mobile"`
	Need_auth      string `json:"need_auth"`
	Allow_send_sms string `json:"allow_send_sms"`
	EncryptedData  string `json:"encryptedData"`
	Iv             string `json:"iv"`
	Cloud_id       string `json:"cloud_id"`
	Code           string `json:"code"`
}
type JSOperateWxDataResponseI struct {
	BaseResponse      *mm.BaseResponse
	JsapiBaseresponse *mm.JSAPIBaseResponse
	Data              string
	Scope             *mm.ScopeInfo
	Appname           *string
	AppiconUrl        *string
	CancelWording     *string
	AllowWording      *string
	ApplyWording      *string
	ALLMobile         []Phoneitem
}

func GetAllMobile(Data JSOperateWxParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid, nil)
	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("异常：%v", err.Error()),
			Data:    nil,
		}
	}
	//sts,_ := base64.StdEncoding.DecodeString("eyJhcGlfbmFtZSI6InFiYXNlX2NvbW1hcGkiLCJkYXRhIjp7InFiYXNlX2FwaV9uYW1lIjoidGNiYXBpX3Nsb3djYWxsZnVuY3Rpb25fdjIiLCJxYmFzZV9yZXEiOiJ7XFwiZnVuY3Rpb25fbmFtZVxcIjpcXCJjcnlwdG9cXCIsXFwiZGF0YVxcIjpcXCJ7XFxcXFxcImlcXFxcXFwiOlxcXFxcXCI3NzQwNlxcXFxcXCIsXFxcXFxcImNcXFxcXFwiOlxcXFxcXCI1MzAxMTFcXFxcXFwiLFxcXFxcXCJ1XFxcXFxcIjpcXFxcXFwidjE1ODk2MzMwNDQzNDcxXFxcXFxcIixcXFxcXFwib1xcXFxcXCI6XFxcXFxcIjQ5NDgyOFxcXFxcXCIsXFxcXFxcInRcXFxcXFwiOjE2MjQwNDI5NjJ9XFwiLFxcImFjdGlvblxcIjoxLFxcInNjZW5lXFwiOjEsXFwiY2FsbF9pZFxcIjpcXCIxNjI0MDM1OTIyMzk2LTAuMzc3MTIyODk4NjExMTEwOFxcIixcXCJjbG91ZGlkX2xpc3RcXCI6W119IiwicWJhc2Vfb3B0aW9ucyI6e30sInFiYXNlX21ldGEiOnsic2Vzc2lvbl9pZCI6IjE2MjQwMzU4MjkyMTgiLCJmaWx0ZXJfdXNlcl9pbmZvIjpmYWxzZSwic2RrX3ZlcnNpb24iOiJ3eC1taW5pcHJvZ3JhbS1zZGtcXC8yLjE3LjMgKDE2MjMzNzg0NzIwMDApIn0sImNsaV9yZXFfaWQiOiIxNjI0MDM1OTIyNDA4XzAuNzQzNjUyMzI4NDg2NzEyMyJ9LCJvcGVyYXRlX2RpcmVjdGx5IjpmYWxzZX0=")
	sts := "{\"with_credentials\":true,\"api_name\":\"webapi_getuserwxphone\"}" // base64.StdEncoding.DecodeString(Data.Data)
	req := &mm.JSOperateWxDataRequest{
		BaseRequest: &mm.BaseRequest{
			SessionKey:    D.Sessionkey,
			Uin:           proto.Uint32(D.Uin),
			DeviceId:      D.Deviceid_byte,
			ClientVersion: proto.Int32(int32(D.ClientVersion)),
			DeviceType:    []byte(D.DeviceType),
			Scene:         proto.Uint32(0),
		},
		Appid:       proto.String(Data.Appid),
		Data:        []byte(sts),
		VersionType: proto.Int32(0),
	}

	reqdata, err := proto.Marshal(req)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("系统异常：%v", err.Error()),
			Data:    nil,
		}
	}

	//发包
	protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
		Ip:     D.Mmtlsip,
		Host:   D.ShortHost,
		Cgiurl: "/cgi-bin/mmbiz-bin/wxaapp/customphone/getallphone",
		Proxy:  D.Proxy,
		PackData: Algorithm.PackData{
			Reqdata:          reqdata,
			Cgi:              0x9E8,
			Uin:              D.Uin,
			Cookie:           D.Cooike,
			Sessionkey:       D.Sessionkey,
			EncryptType:      5,
			Loginecdhkey:     D.RsaPublicKey,
			Clientsessionkey: D.Clientsessionkey,
			UseCompress:      false,
		},
	}, D.MmtlsKey)

	if err != nil {
		return models.ResponseResult{
			Code:    errtype,
			Success: false,
			Message: err.Error(),
			Data:    nil,
		}
	}

	//解包
	Response := mm.JSOperateWxDataResponse{}
	err = proto.Unmarshal(protobufdata, &Response)

	if err != nil {
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
			Data:    nil,
		}
	}
	its := make([]Phoneitem, 0)
	strdata := string(Response.Data)
	replacedData := strings.Replace(strdata, "\"data\":\"{\\\"code\\\":\\\"", "\"code\":\"", -1)
	replacedData = strings.Replace(replacedData, "\\\"}\"}", "\"}", -1)
	replacedData = strings.Replace(replacedData, "{\"wx_phone\":", "[", -1)
	replacedData = strings.Replace(replacedData, "\"custom_phone_list\":[{", "{", -1)
	replacedData = strings.Replace(replacedData, "}]}", "}]", -1)
	replacedData = strings.Replace(replacedData, "true", "1", -1)
	replacedData = strings.Replace(replacedData, "false", "0", -1)
	replacedData = strings.Replace(replacedData, "}}", "}]", -1)
	json.Unmarshal([]byte(replacedData), &its)
	rep := JSOperateWxDataResponseI{
		BaseResponse:      Response.BaseResponse,
		JsapiBaseresponse: Response.JsapiBaseresponse,
		Data:              string(Response.Data),
		Scope:             Response.Scope,
		Appname:           Response.Appname,
		AppiconUrl:        Response.AppiconUrl,
		CancelWording:     Response.CancelWording,
		AllowWording:      Response.AllowWording,
		ApplyWording:      Response.ApplyWording,
		ALLMobile:         its,
	}
	return models.ResponseResult{
		Code:    0,
		Success: true,
		Message: "成功",
		Data:    rep,
	}
}

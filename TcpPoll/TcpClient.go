package TcpPoll

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/Mmtls"
	"wechatdll/comm"
	"wechatdll/lib"
	"wechatdll/models"
wechatdll/models/Login
	"wechatdll/models/Msg"

	"github.com/astaxie/beego"
	"github.com/golang/protobuf/proto"
	log "github.com/sirupsen/logrus"
	"golang.org/x/net/proxy"
)

// bytes.Buffer池，用于数据组装（保留用于其他地方）
var (
	bytesBufferPool = sync.Pool{
		New: func() interface{} {
			return &bytes.Buffer{}
		},
	}
)

type TcpClient struct {
	sync.Mutex                             // 添加互斥锁用于并发控制
	model             *comm.LoginData      // wx缓存
	receivedBytes     []byte               // 接收数据缓存区（原始代码字段）
	pskData           [][]byte             // 握手队列
	handshaking       bool                 // 是否握手中
	mmtlsPrivateKey1  []byte               // 1次握手密钥
	mmtlsPublicKey1   []byte               // 1次握手公钥
	mmtlsPrivateKey2  []byte               // 1次握手密钥
	mmtlsPublicKey2   []byte               // 1次握手公钥
	handshakeHash     []byte               // 握手hash值
	longLinkEncodeIv  []byte               // 握手加密iv
	longLinkEncodeKey []byte               // 握手加密key
	longLinkDecodeIv  []byte               // 握手解密iv
	longLinkDecodeKey []byte               // 握手解密key
	shareKey          []byte               // 协商密钥
	serverSequence    int                  // 服务端发包序列
	clientSequence    int                  // 客户端发包序列
	mmtlsSequence     int                  // mmtls组包序列
	pskKey            []byte               // psk密钥
	queue             map[int]func([]byte) // 回调序列
	messageCache      []byte               // 消息缓存
	conn              net.Conn             // 长连接
	stopChan          chan struct{}        // 停止通道
	stopped           bool                 // 是否已停止
	hasError          bool                 // 是否有连接错误
	readerStopped     bool                 // 读goroutine是否已停止

	// 849风格的智能心跳机制
	heartBeatTimer *time.Timer       // 心跳定时器
	autoAuthTimer  *time.Timer       // 二次登录定时器
	isConnected    bool              // 是否连接着
	heartBeatTime  int64             // 心跳时间
	startDateTime  int64             // 开始的时间戳
	longReqQueue   chan *LongRequest // 长连接请求队列
	exitFlagChan   chan bool         // 断开链接通道
}

// LongRequest 长连接请求结构体
type LongRequest struct {
	OpCode int    // 操作码
	Data   []byte // 数据
}

// GetOpcode 获取操作码
func (lr *LongRequest) GetOpcode() int {
	return lr.OpCode
}

// GetData 获取数据
func (lr *LongRequest) GetData() []byte {
	return lr.Data
}

var CORRECT_HEADER = []byte{0x00, 0xf1, 0x03}

// 849风格智能心跳日志文件
var smartHeartbeatLogFile *os.File

// LogSmartHeartbeat 记录849风格智能心跳日志到文件
func LogSmartHeartbeat(wxid, message string) {
	// 初始化日志文件（如果还没有）
	if smartHeartbeatLogFile == nil {
		now := time.Now()
		logFileName := fmt.Sprintf("849_smart_heartbeat_%s.log", now.Format("2006-01-02"))
		file, err := os.OpenFile(logFileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return // 创建失败，静默忽略
		}
		smartHeartbeatLogFile = file
	}

	// 写入日志
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logLine := fmt.Sprintf("[%s] [%s] %s\n", timestamp, wxid, message)
	smartHeartbeatLogFile.WriteString(logLine)
	smartHeartbeatLogFile.Sync() // 立即刷新到磁盘
}

func NewTcpClient(model *comm.LoginData) *TcpClient {
	instance := TcpClient{
		model: model,
	}
	instance.handshaking = true // 新建的连接先需要握手
	instance.mmtlsPrivateKey1, instance.mmtlsPublicKey1 = Algorithm.GetECDH415Key()
	instance.mmtlsPrivateKey2, instance.mmtlsPublicKey2 = Algorithm.GetECDH415Key()
	instance.serverSequence = 1
	instance.clientSequence = 1
	instance.mmtlsSequence = 1
	instance.queue = make(map[int]func([]byte))
	instance.stopChan = make(chan struct{})
	instance.stopped = false
	instance.hasError = false

	// 初始化849风格的智能心跳机制字段
	instance.isConnected = false
	instance.heartBeatTime = 0
	instance.startDateTime = time.Now().Unix()
	instance.longReqQueue = make(chan *LongRequest, 5000)
	instance.exitFlagChan = make(chan bool, 1)

	// 849风格智能心跳日志记录到文件
	LogSmartHeartbeat(model.Wxid, "🚀 849风格智能心跳机制初始化完成")

	return &instance
}

// 创建长连接. remoteAddr string: 目标地址, proxyAddr string: 代理地址, proxyUser string: 代理用户, proxyPassword string: 代理密码
func CreateConnection(remoteAddr string, proxyAddr string, proxyUser string, proxyPassword string) (net.Conn, error) {
	// 判断proxy是否需要验证密码, 如果不需要则proxyAuth为空指针, 否则指向初始化的proxy.Auth结构
	var proxyAuth *proxy.Auth = nil
	if proxyUser != "" {
		proxyAuth = &proxy.Auth{
			User:     proxyUser,
			Password: proxyPassword,
		}
	}
	var conn net.Conn
	var connErr error
	if proxyAddr != "" {
		// 创建proxy连接到远程服务器
		dialer, dialErr := proxy.SOCKS5("tcp", proxyAddr, proxyAuth, proxy.Direct)
		if dialErr != nil {
			return nil, dialErr
		}
		conn, connErr = dialer.Dial("tcp", remoteAddr+":80")
		if connErr != nil {
			return nil, connErr
		}
	} else {
		// 直接连接远程服务器
		conn, connErr = net.Dial("tcp", remoteAddr)
		if connErr != nil {
			return nil, connErr
		}
	}

	// 849风格：不设置TCP keep-alive，只依赖应用层心跳机制
	return conn, nil
}

// 根据wx缓存创建并连接到长连接
func (client *TcpClient) Connect() error {
	var connErr error
	client.conn, connErr = CreateConnection(
		client.model.MarsHost,
		client.model.Proxy.ProxyIp,
		client.model.Proxy.ProxyUser,
		client.model.Proxy.ProxyPassword)
	if connErr != nil {
		if strings.Contains(connErr.Error(), "missing port in address") {
			client.conn, connErr = CreateConnection(
				client.model.MarsHost+":80",
				client.model.Proxy.ProxyIp,
				client.model.Proxy.ProxyUser,
				client.model.Proxy.ProxyPassword)
			if connErr != nil {
				return connErr
			}
		} else {
			return connErr
		}
	}

	helloWrapper, helloBuff := client.BuildClientHello()
	client.handshakeHash = append(client.handshakeHash, helloBuff...)
	client.Send(helloWrapper, "客户端握手开始")
	return nil
}

// 处理接收到的消息, 包括mmtls解包和ecdh密钥交换
func (client *TcpClient) ProcessMessage(messageBytes []byte) {
	log.Infof("TcpClient: ProcessMessage执行, 处理消息{%v}[%x]", len(messageBytes), messageBytes)
	if client.handshaking {
		// 握手阶段的消息处理
		if len(client.pskData) < 5 {
			// 握手交互小于5次, 将消息放入握手队列
			client.pskData = append(client.pskData, messageBytes)
		}
		if len(client.pskData) == 4 {
			// 前四次握手交互
			if err := client.ProcessServerHello(client.pskData[0]); err != nil {
				log.Errorf("第一次握手密钥协商错误")
				return
			}
			if err := client.GetCertificateVerify(client.pskData[1]); err != nil {
				log.Errorf("第二次握手密钥协商错误")
				return
			}
			if err := client.BuildServerTicket(client.pskData[2]); err != nil {
				log.Errorf("第三次握手密钥协商错误")
				return
			}
			if err := client.ServerFinish(client.pskData[3]); err != nil {
				log.Errorf("第四次握手密钥协商错误")
				return
			}
			clientFinishPackage := client.ClientFinish(client.pskData[3])
			log.Infof("ClientFinish: %x", clientFinishPackage)
			if clientFinishPackage == nil {
				log.Errorf("ClientFinish失败")
				return
			}
			client.Send(clientFinishPackage, "客户端握手结束")
			client.handshaking = false
			client.isConnected = true
			// 启动849风格的智能心跳机制
			client.heartBeatTimer = time.NewTimer(time.Second * 175) // 初始175秒间隔
			client.autoAuthTimer = time.NewTimer(time.Minute * 1)    // 1分钟二次登录（首次快速触发）

			// 849风格智能心跳机制启动

			go client.startLongWriter()
		}
	} else {
		if messageBytes[0] == 0x17 {
			// mmtls解包并回调
			message := client.UnpackMmtlsLong(messageBytes)
			log.Infof("TcpClient: ProcessMessage执行, 消息[%x]mmtls解密为[%x]", messageBytes, message)
			client.HandleMessage(message)
		} else if messageBytes[0] == 0x15 {
			log.Infof("TcpClient: ProcessMessage执行, 终止长连接[%x]", messageBytes)
			client.Terminate()
		}
	}
}

// ----------------------------- mmtls握手内容 --------------------------------------
// 发起握手组包
func (client *TcpClient) BuildClientHello() ([]byte, []byte) {
	var helloBuff = new(bytes.Buffer)
	// part_1: 固定头, 10 - 0 = 10位
	helloBuff.Write([]byte{0x0, 0x0, 0x0, 0xd0, 0x1, 0x3, 0xf1, 0x1, 0xc0, 0x2b})
	// part_2: 随机32位bytes, 42 - 10 = 32位
	helloBuff.Write([]byte(lib.RandSeq(32)))
	// part_3: 时间戳, 46 - 42 = 4位
	time := time.Now().Unix()
	binary.Write(helloBuff, binary.BigEndian, (int32)(time))
	// part_4: 未知数据, 这里写死, 58 - 46 = 12位, TODO: 搞明白这里怎么模拟
	helloBuff.Write([]byte{0x00, 0x00, 0x00, 0xA2, 0x01, 0x00, 0x00, 0x00, 0x9D, 0x00, 0x10, 0x02})
	// part_5: 第一个公钥, 序列和长度一共6字节,所以这里长度是公钥长度加6, 62 - 58 = 4位
	binary.Write(helloBuff, binary.BigEndian, (int32)(len(client.mmtlsPublicKey1)+6))
	// part_6: 公钥序号, 66 - 62 = 4位
	binary.Write(helloBuff, binary.BigEndian, (int32)(1))
	// part_7: 公钥长度, 68 - 66 = 2位
	binary.Write(helloBuff, binary.BigEndian, (int16)(len(client.mmtlsPublicKey1)))
	// part_8: 第一个公钥值, 133 - 68 = 65位
	helloBuff.Write(client.mmtlsPublicKey1)
	// part_9: 第二个公钥, 137 - 133 = 4位
	binary.Write(helloBuff, binary.BigEndian, (int32)(len(client.mmtlsPublicKey2)+6))
	// part_10: 公钥序号, 141 - 137 = 4位
	binary.Write(helloBuff, binary.BigEndian, (int32)(2))
	// part_11: 公钥长度, 143 - 141 = 2位
	binary.Write(helloBuff, binary.BigEndian, (int16)(len(client.mmtlsPublicKey2)))
	// part_12: 第二个公钥值, 208 - 143 = 65位
	helloBuff.Write(client.mmtlsPublicKey2)
	// part_13: 尾部1, 212 - 208 = 4位
	binary.Write(helloBuff, binary.BigEndian, (int32)(1))
	// 外层包装, 215 - 212 = 3位
	var helloWrapper = new(bytes.Buffer)
	helloWrapper.Write([]byte{0x16, 0xf1, 0x03})
	binary.Write(helloWrapper, binary.BigEndian, (int16)(len(helloBuff.Bytes())))
	helloWrapper.Write(helloBuff.Bytes())
	return helloWrapper.Bytes(), helloBuff.Bytes()
}

// mmtls握手第一个收到的消息是ServerHello
func (client *TcpClient) ProcessServerHello(message []byte) error {
	keyPairSequence := binary.BigEndian.Uint32(message[57:61]) // 确定是使用密钥对1还是密钥对2
	serverPublicKey := message[63:128]                         // 从63位置取出65位服务端公钥
	privateKey := client.mmtlsPrivateKey1
	if keyPairSequence == 2 {
		privateKey = client.mmtlsPrivateKey2
	}
	shareKey := Algorithm.DoECDH415Key(privateKey, serverPublicKey)
	if shareKey == nil {
		return errors.New("Mmtls: 秘钥交互失败")
	} else if len(shareKey) != 32 {
		return errors.New("Mmtls: 交互秘钥长度存在异常")
	}
	client.shareKey = Mmtls.Getsha256(shareKey) // 协商再hash得到共享密钥
	client.handshakeHash = append(client.handshakeHash, message[5:]...)

	var infoBytes = new(bytes.Buffer)
	infoBytes.Write(Mmtls.Utf8ToBytes("handshake key expansion"))
	dataHash := Mmtls.Getsha256(client.handshakeHash)
	infoBytes.Write(dataHash)
	expandKey := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, infoBytes.Bytes(), 56)
	//fmt.Println(len(hkdfexpandkey))
	client.longLinkEncodeKey = expandKey[:16]
	client.longLinkDecodeKey = expandKey[16:32]
	client.longLinkEncodeIv = expandKey[32:44]
	client.longLinkDecodeIv = expandKey[44:]
	return nil
}

// 使用IV和key解码第二次握手内容, 把结果放入hand_shake_hash
func (client *TcpClient) GetCertificateVerify(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节,包括后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	iv, client.serverSequence = Mmtls.GetDecryptIv(client.longLinkDecodeIv, client.serverSequence)
	serverSecret := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if serverSecret == nil {
		err := errors.New("第二次握手密钥协商错误")
		return err
	}
	client.handshakeHash = append(client.handshakeHash, serverSecret...)
	return nil
}

// 解密第三次握手内容
func (client *TcpClient) BuildServerTicket(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节和后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	iv, client.serverSequence = Mmtls.GetDecryptIv(client.longLinkDecodeIv, client.serverSequence)
	client.pskKey = Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if client.pskKey == nil {
		err := errors.New("第三次握手密钥协商错误")
		return err
	}
	// client.build_short_link_ticket() 这里不需要短连接ticket了,短连接可自己握手
	client.handshakeHash = append(client.handshakeHash, client.pskKey...)
	return nil
}

// 解密第四次握手内容
func (client *TcpClient) ServerFinish(message []byte) error {
	pskLen := len(message)
	// 去除开头的5字节和后面16字节的tag
	dataSection := message[5:pskLen]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, message[0:5]...)
	var iv []byte
	iv, client.serverSequence = Mmtls.GetDecryptIv(client.longLinkDecodeIv, client.serverSequence)
	serverFinishData := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, dataSection, iv, aad)
	if serverFinishData == nil {
		err := errors.New("第四次握手密钥协商错误")
		return err
	}
	return nil
}

// mmtls握手完成组包
func (client *TcpClient) ClientFinish(message []byte) []byte {
	var infoBytes = new(bytes.Buffer)
	infoBytes.Write(Mmtls.Utf8ToBytes("client finished"))
	dataHash := Mmtls.Getsha256(client.handshakeHash)
	// infoBytes.Write(dataHash)
	clientFinishData := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, infoBytes.Bytes(), 32)
	hmacHash := hmac.New(sha256.New, clientFinishData)
	hmacHash.Write(dataHash)
	hmacRet := hmacHash.Sum(nil)
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.clientSequence))
	aad = append(aad, message[0:5]...)
	sendData := []byte{0x00, 0x00, 0x00, 0x23, 0x14, 0x00, 0x20}
	sendData = append(sendData, hmacRet...)
	var iv []byte
	iv, client.clientSequence = Mmtls.GetEncryptIv(client.longLinkEncodeIv, client.clientSequence)
	clientFinishByte := Algorithm.NewAES_GCMEncrypter(client.longLinkEncodeKey, sendData, iv, aad)
	var clientFinishWrapperBuffer = new(bytes.Buffer)
	clientFinishWrapperBuffer.Write([]byte{0x16, 0xf1, 0x03})
	var finishLengthByte = []byte{0, 0}
	binary.BigEndian.PutUint16(finishLengthByte, uint16(len(clientFinishByte)))
	clientFinishWrapperBuffer.Write(finishLengthByte)
	clientFinishWrapperBuffer.Write(clientFinishByte)
	var secretBuffer = new(bytes.Buffer)
	secretBuffer.Write(Mmtls.Utf8ToBytes("expanded secret"))
	secretBuffer.Write(dataHash)
	secretData := Algorithm.Hkdf_Expand(sha256.New, client.shareKey, secretBuffer.Bytes(), 32)
	var expandBuffer = new(bytes.Buffer)
	expandBuffer.Write(Mmtls.Utf8ToBytes("application data key expansion"))
	expandBuffer.Write(dataHash)
	expandData := Algorithm.Hkdf_Expand(sha256.New, secretData, expandBuffer.Bytes(), 56)
	client.longLinkEncodeKey = expandData[0:16]
	client.longLinkDecodeKey = expandData[16:32]
	client.longLinkEncodeIv = expandData[32:44]
	client.longLinkDecodeIv = expandData[44:]
	return clientFinishWrapperBuffer.Bytes()
}

// ------------------------------ 组包解包处理 -------------------------------------------
// mmtls加密
func (client *TcpClient) PackMmtlsLong(plainMessage []byte) ([]byte, error) {
	log.Infof("TcpClient: PackMmtlsLong: message(%v)[%x]", len(plainMessage), plainMessage)
	var nonce []byte

	// 从对象池获取Buffer，减少内存分配
	aadBuffer := bytesBufferPool.Get().(*bytes.Buffer)
	defer func() {
		aadBuffer.Reset()
		bytesBufferPool.Put(aadBuffer)
	}()

	binary.Write(aadBuffer, binary.BigEndian, uint64(client.clientSequence))
	nonce, client.clientSequence = Mmtls.GetNonce(client.longLinkEncodeIv, client.clientSequence)
	aadBuffer.Write([]byte{0x17, 0xf1, 0x03})
	binary.Write(aadBuffer, binary.BigEndian, uint16(len(plainMessage)+16))
	cipherMessage := Algorithm.NewAES_GCMEncrypter(client.longLinkEncodeKey, plainMessage, nonce, aadBuffer.Bytes())
	if cipherMessage == nil {
		return nil, errors.New("AESGCM加密消息失败")
	}

	// 从对象池获取第二个Buffer
	wrapBufferInterface := bytesBufferPool.Get()
	wrapBuffer, ok := wrapBufferInterface.(*bytes.Buffer)
	if !ok {
		return nil, errors.New("对象池类型断言失败，期望*bytes.Buffer")
	}
	defer func() {
		wrapBuffer.Reset()
		bytesBufferPool.Put(wrapBuffer)
	}()

	wrapBuffer.Write([]byte{0x17, 0xf1, 0x03})
	binary.Write(wrapBuffer, binary.BigEndian, uint16(len(cipherMessage)))
	wrapBuffer.Write(cipherMessage)

	// 复制结果，避免引用池中的Buffer
	result := make([]byte, wrapBuffer.Len())
	copy(result, wrapBuffer.Bytes())
	return result, nil
}

// mmtls解密
func (client *TcpClient) UnpackMmtlsLong(messageBytes []byte) []byte {
	packData := messageBytes[5:]
	var aad = []byte{0, 0, 0, 0, 0, 0, 0, 0}
	binary.BigEndian.PutUint64(aad, uint64(client.serverSequence))
	aad = append(aad, messageBytes[0:5]...)
	var iv []byte
	iv, client.serverSequence = Mmtls.GetDecryptIv(client.longLinkDecodeIv, client.serverSequence)
	ret := Algorithm.NewAES_GCMDecrypter(client.longLinkDecodeKey, packData, iv, aad)
	return ret
}

// 组包头
func BuildWrapper(message []byte, cmdId int, mmtlsSeq int) []byte {
	// 从对象池获取Buffer，减少内存分配
	dataWrapperInterface := bytesBufferPool.Get()
	dataWrapper, ok := dataWrapperInterface.(*bytes.Buffer)
	if !ok {
		log.Errorf("BuildWrapper: 对象池类型断言失败，期望*bytes.Buffer")
		return nil
	}
	defer func() {
		dataWrapper.Reset()
		bytesBufferPool.Put(dataWrapper)
	}()

	binary.Write(dataWrapper, binary.BigEndian, int32(len(message)+16)) // 包头组成1: 总长度
	binary.Write(dataWrapper, binary.BigEndian, int16(16))              // 包头组成2: 头部长度
	binary.Write(dataWrapper, binary.BigEndian, int16(1))               // 包头组成3: 组包版本号
	binary.Write(dataWrapper, binary.BigEndian, int32(cmdId))           // 包头组成4: 操作命令ID
	binary.Write(dataWrapper, binary.BigEndian, int32(mmtlsSeq))        // 包头组成5: 组包序列
	dataWrapper.Write(message)

	// 复制结果，避免引用池中的Buffer
	result := make([]byte, dataWrapper.Len())
	copy(result, dataWrapper.Bytes())
	return result
}

// 解包头
func StripWrapper(message []byte, length int) ([]byte, int, int, int, int, int) {
	totalLength := binary.BigEndian.Uint32(message[:4])
	headLength := binary.BigEndian.Uint16(message[4:6])
	packVersion := binary.BigEndian.Uint16(message[6:8])
	cmdId := binary.BigEndian.Uint32(message[8:12])
	packSequence := binary.BigEndian.Uint32(message[12:16])
	packData := message[16:]
	return packData, int(cmdId), int(packSequence), int(packVersion), int(headLength), int(totalLength)
}

// ------------------------------ 消息执行 ----------------------------------------------
// 处理消息
func (client *TcpClient) HandleMessage(message []byte) {
	if len(message) > 0 {
		message = append(client.messageCache, message...)
	}
	messageBody, cmdId, packSequence, packVersion, headLength, totalLength := StripWrapper(message, len(message))
	// 超长的数据包会分包发送, 这里使用缓存将分包归总
	if totalLength > len(message) {
		log.Infof("长包跟踪: 长度不对缓存不处理: 预计长度[%d], 实际长度[%d]", totalLength, len(message))
		client.messageCache = message
		return
	} else {
		client.messageCache = []byte{}
	}
	if cmdId != 1000000006 {
		log.Infof("TcpClient: handle_message收到消息{cmd_id: %v, pack_seq: %v, pack_version: %v, head_length: %v, total_length: %v}", cmdId, packSequence, packVersion, headLength, totalLength)
	}
	if cmdId == 24 {
		status := binary.BigEndian.Uint32(messageBody)
		// TODO: 回调SyncMessage
		now := time.Now()
		// 格式化到毫秒，注意Go的时间格式化字符串中毫秒是"0ms"
		formatted := now.Format("2006-01-02 15:04:05.000")
		// 打印格式化后的时间
		log.Infof("当前时间:" + formatted)
		log.Infof("收到24消息提醒, status[%d], 执行回调", status)
		WXDATA := Msg.Sync(Msg.SyncParam{Wxid: client.model.Wxid, Synckey: "", Scene: 0})
		jsonValue, _ := json.Marshal(WXDATA)
		syncUrl := strings.Replace(client.model.MsgUrl, "{0}", client.model.Wxid, -1)
		//go comm.HttpPost(syncUrl, *new(url.Values), nil, "", "", "", "")
		reqBody := strings.NewReader(string(jsonValue))
		nows := time.Now()
		// 格式化到毫秒，注意Go的时间格式化字符串中毫秒是"0ms"
		formatteds := nows.Format("2006-01-02 15:04:05.000")
		log.Infof("消息时间:" + formatteds)
		log.Infof("消息内容" + string(jsonValue))
		go comm.HttpPosthb(syncUrl, reqBody, nil, "", "", "", "")
		return
	}

	// 处理238心跳响应
	if cmdId == 238 {
		log.Infof("💓 TcpClient: [%s] 收到238心跳响应，开始处理", client.model.Wxid)
		client.handleHeartBeatResponse(messageBody)
		return
	}

	// 回调
	cb := client.queue[packSequence]
	if cb != nil {
		delete(client.queue, packSequence)
		cb(messageBody)
	}
}

// 发送数据
func (client *TcpClient) Send(data []byte, tag string) {
	// 849风格：移除了TCP心跳，所有发送都记录日志
	log.Infof("TcpClient: Send执行[%s], 发送消息(%v)[%x...]", tag, len(data), data[:32])
	_, err := client.conn.Write(data)
	if err != nil {
		log.Errorf("TcpClient: 发送数据失败[%s][%s]: %v", client.model.Wxid, tag, err)

		// 849风格：如果启用了智能心跳机制，不设置hasError，让心跳机制处理
		if client.heartBeatTimer == nil {
			// 传统方式：没有智能心跳机制时设置hasError
			client.hasError = true
		}
		// 849风格：有智能心跳机制时不设置hasError，由心跳超时机制处理
	}
}

// mmtls发包
func (client *TcpClient) MmtlsSend(data []byte, cmdId int, tag string) (*[]byte, error) {
	//for client.handshaking {
	//	time.Sleep(100 * time.Millisecond)
	//}
	// mmtls组包头
	dataWrapper := BuildWrapper(data, cmdId, client.mmtlsSequence) // mmtls加密
	sendData, err := client.PackMmtlsLong(dataWrapper)
	if err != nil {
		return nil, err
	}
	// 使用channel替代轮询等待，提高性能
	respChan := make(chan []byte, 1)
	client.queue[client.mmtlsSequence] = func(recv []byte) {
		select {
		case respChan <- recv:
		default:
			// 如果channel满了，说明有问题，但不阻塞
		}
	}
	// 组包序列自增
	client.mmtlsSequence++
	// 发包
	client.Send(sendData, tag)
	// 使用select等待结果，避免轮询
	timeoutSpan, _ := time.ParseDuration(beego.AppConfig.String("longlinkconnecttimeout"))

	var resp []byte
	select {
	case resp = <-respChan:
		// 收到响应
	case <-time.After(timeoutSpan):
		// 清理回调函数，避免内存泄漏
		delete(client.queue, client.mmtlsSequence-1)
		return nil, errors.New("请求超时")
	}

	if len(resp) < 32 {
		// 长度小于32, 用户session已失效
		return nil, errors.New("用户可能退出")
	}
	unpackData := Algorithm.UnpackBusinessPacket(resp, client.model.Sessionkey, client.model.Uin, &client.model.Cooike)
	// 将cookie更新保存到redis
	err = comm.CreateLoginData(*client.model, client.model.Wxid, 0)
	if err != nil {
		log.Errorf("TcpClient: MmtlsSend回调时更新redis失败[%v]", err.Error())
		return nil, err
	}
	return &unpackData, nil
}

func (client *TcpClient) Terminate() {
	client.Lock()
	defer client.Unlock()

	if client.stopped {
		return
	}

	client.stopped = true

	// 关闭停止通道，通知所有goroutine退出
	close(client.stopChan)

	// 关闭TCP连接
	if client.conn != nil {
		err := client.conn.Close()
		if err != nil {
			log.Errorf("TcpClient: 关闭连接失败[%v]", err.Error())
		}
		client.conn = nil
	}

	// 清理资源
	client.cleanup()
}

// 清理客户端资源
func (client *TcpClient) cleanup() {
	// 清理缓存数据（协议层优化后简化）
	client.pskData = nil
	client.handshakeHash = nil
	client.messageCache = nil

	// 清理密钥数据
	client.mmtlsPrivateKey1 = nil
	client.mmtlsPublicKey1 = nil
	client.mmtlsPrivateKey2 = nil
	client.mmtlsPublicKey2 = nil
	client.longLinkEncodeIv = nil
	client.longLinkEncodeKey = nil
	client.longLinkDecodeIv = nil
	client.longLinkDecodeKey = nil
	client.shareKey = nil
	client.pskKey = nil

	// 清理回调队列
	if client.queue != nil {
		for k := range client.queue {
			delete(client.queue, k)
		}
		client.queue = nil
	}

	// 849风格智能心跳机制：只在明确停止时清理，否则保持心跳机制运行
	if client.isConnected && client.heartBeatTimer != nil && !client.stopped {
		LogSmartHeartbeat(client.model.Wxid, "🔄 连接出现问题但保持849风格智能心跳机制运行")
		client.isConnected = false
		// 不清理心跳资源，让心跳机制自己检测并处理重连
		return
	}

	// 只有在明确停止时才清理849风格的智能心跳机制资源
	if client.heartBeatTimer != nil {
		client.heartBeatTimer.Stop()
		client.heartBeatTimer = nil
	}
	if client.autoAuthTimer != nil {
		client.autoAuthTimer.Stop()
		client.autoAuthTimer = nil
	}

	// 清理通道
	if client.longReqQueue != nil {
		close(client.longReqQueue)
		client.longReqQueue = nil
	}
	if client.exitFlagChan != nil {
		close(client.exitFlagChan)
		client.exitFlagChan = nil
	}
}

// startLongWriter 849风格的长连接写入处理器
func (client *TcpClient) startLongWriter() {
	startDateTime := client.startDateTime
	// 849风格智能心跳日志记录到文件
	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🔄 startLongWriter启动，startDateTime=%d", startDateTime))

	for {
		// 判断链接断开立马暂停
		if !client.isConnected {
			log.Infof("🔌 TcpClient: [%s] 连接已断开，退出startLongWriter", client.model.Wxid)
			break
		}

		// 判断是否重复启动 for 循环
		if startDateTime != client.startDateTime {
			log.Infof("🔄 TcpClient: [%s] 检测到重复启动，退出旧的startLongWriter (old=%d, new=%d)",
				client.model.Wxid, startDateTime, client.startDateTime)
			break
		}

		select {
		case longReq := <-client.longReqQueue:
			// 心跳和登录都会走这里
			log.Infof("📤 TcpClient: [%s] 处理长连接请求，OpCode=%d", client.model.Wxid, longReq.GetOpcode())
			err := client.sendLongRequest(longReq)
			if err != nil {
				// 断开链接
				log.Errorf("❌ TcpClient: [%s] 长连接请求发送失败 - %s", client.model.Wxid, err.Error())
				// 判断是心跳出错 - 触发重连
				if longReq.GetOpcode() == 238 { // 238是心跳包的cmdId
					log.Warnf("💔 TcpClient: [%s] 238心跳包发送失败，200ms后触发重连", client.model.Wxid)
					// 等 200 毫秒
					time.Sleep(200 * time.Millisecond)
					if startDateTime == client.startDateTime {
						go client.restartConnection()
					}
				} else {
					log.Errorf("🚫 TcpClient: [%s] 非心跳包发送失败，直接断开连接", client.model.Wxid)
					client.Terminate() // 断开链接
				}
			} else {
				if longReq.GetOpcode() == 238 {
					log.Infof("💓 TcpClient: [%s] 238心跳包发送成功", client.model.Wxid)
				}
			}
			continue
		case <-client.heartBeatTimer.C:
			// 849风格：异步调用HTTP HeartBeat接口，避免阻塞主循环
			if client.isConnected {
				// 异步执行HTTP心跳，避免阻塞主循环
				go func() {
					nextTime := client.callHttpHeartBeat()

					// 异步重置定时器
					if client.heartBeatTimer != nil {
						client.heartBeatTimer.Reset(time.Second * time.Duration(nextTime))
					}

					// 更新心跳时间
					client.heartBeatTime = time.Now().Unix()
				}()
			} else {
				// 连接断开时也要重置定时器，继续尝试
				if client.heartBeatTimer != nil {
					client.heartBeatTimer.Reset(time.Second * 175)
				}
			}
			continue
		case <-client.autoAuthTimer.C:
			// 异步调用835项目的二次登录接口，避免阻塞主循环
			minutes := uint32(30 + rand.Int31n(30))
			LogSmartHeartbeat(client.model.Wxid, "🔐 二次登录定时器触发，异步调用835的Secautoauth接口")

			// 异步执行二次登录，避免阻塞主循环
			go func() {
				LogSmartHeartbeat(client.model.Wxid, "🔐 开始执行二次登录Secautoauth接口")

				// 直接调用内部实现的Secautoauth函数
				result := client.callSecautoauth()

				// 详细打印返回结果
				LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🔐 二次登录完成 - Code: %d, Success: %t", result.Code, result.Success))
				LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🔐 二次登录消息: %s", result.Message))

				if result.Success {
					LogSmartHeartbeat(client.model.Wxid, "✅ 二次登录成功！")
					if result.Data != nil {
						LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🔐 二次登录返回数据: %+v", result.Data))
					}
				} else {
					LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 二次登录失败: %s", result.Message))

					// 849风格：二次登录失败时清理长连接（与849项目一致）
					if result.Code == -8 || strings.Contains(result.Message, "退出") {
						LogSmartHeartbeat(client.model.Wxid, "🚨 Session会话过期，清理长连接（849风格处理）")
						// 触发重连机制
						client.Lock()
						client.hasError = true
						client.isConnected = false
						client.Unlock()

						// 发送退出信号，清理当前连接
						select {
						case client.exitFlagChan <- true:
							LogSmartHeartbeat(client.model.Wxid, "📤 已发送退出信号，将触发重连")
						default:
							LogSmartHeartbeat(client.model.Wxid, "⚠️ 退出信号通道已满，连接可能已在清理中")
						}
					}
				}
			}()

			// 主循环立即重置定时器，不等待二次登录完成
			LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("⏰ 二次登录定时器重置为%d分钟后", minutes))
			client.autoAuthTimer.Reset(time.Minute * time.Duration(minutes))
			continue
		case <-client.exitFlagChan:
			LogSmartHeartbeat(client.model.Wxid, "🚪 收到退出信号，startLongWriter退出")
			return
		case <-client.stopChan:
			LogSmartHeartbeat(client.model.Wxid, "🛑 收到停止信号，startLongWriter退出")
			return
		}
	}
}

// sendLongRequest 发送长连接请求
func (client *TcpClient) sendLongRequest(longReq *LongRequest) error {
	// mmtls组包头
	dataWrapper := BuildWrapper(longReq.GetData(), longReq.GetOpcode(), client.mmtlsSequence)
	sendData, err := client.PackMmtlsLong(dataWrapper)
	if err != nil {
		return err
	}

	// 发包
	client.Send(sendData, fmt.Sprintf("%d请求", longReq.GetOpcode()))
	client.mmtlsSequence++
	return nil
}

// 849风格：移除了TCP心跳包发送，改为HTTP心跳接口

// callHttpHeartBeat 直接调用835原有的HeartBeat函数
func (client *TcpClient) callHttpHeartBeat() int {
	LogSmartHeartbeat(client.model.Wxid, "📞 开始调用835原有的HeartBeat函数")

	// 需要导入Login包来调用HeartBeat函数
	// 但这会造成循环依赖，所以暂时使用HTTP接口
	// TODO: 重构包结构以避免循环依赖

	// 动态获取服务器地址和端口
	httpAddr := beego.AppConfig.String("httpaddr")
	if httpAddr == "" {
		httpAddr = "127.0.0.1" // 默认本地地址
	}

	httpPort := beego.AppConfig.String("httpport")
	if httpPort == "" {
		httpPort = "8080" // 默认端口
	}

	// 构造HTTP请求URL
	baseURL := fmt.Sprintf("http://%s:%s/api/Login/HeartBeat", httpAddr, httpPort)
	params := url.Values{}
	params.Set("wxid", client.model.Wxid)

	fullURL := baseURL + "?" + params.Encode()
	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🌐 心跳请求URL: %s", fullURL))

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送POST请求
	resp, err := httpClient.Post(fullURL, "application/x-www-form-urlencoded", nil)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 心跳请求失败: %v", err))
		return 175 // 失败时使用默认间隔
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 读取心跳响应失败: %v", err))
		return 175 // 失败时使用默认间隔
	}

	// 解析JSON响应
	var result models.ResponseResult
	err = json.Unmarshal(body, &result)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 解析心跳响应失败: %v", err))
		return 175 // 失败时使用默认间隔
	}

	if !result.Success {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 心跳失败: %s", result.Message))
		return 175 // 失败时使用默认间隔
	}

	// 尝试从响应数据中获取NextTime
	nextTime := int(175) // 默认值
	if result.Data != nil {
		// 如果Data是HeartBeatResponse类型，尝试获取NextTime
		if dataMap, ok := result.Data.(map[string]interface{}); ok {
			if nextTimeValue, exists := dataMap["NextTime"]; exists {
				if nextTimeInt, ok := nextTimeValue.(float64); ok {
					nextTime = int(nextTimeInt)
					LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("✅ 心跳成功，服务器返回NextTime: %d秒", nextTime))
				}
			}
		}
	}

	if nextTime == 175 {
		LogSmartHeartbeat(client.model.Wxid, "✅ 心跳成功，使用默认间隔175秒")
	}

	// 确保NextTime在合理范围内（60秒到600秒）
	if nextTime < 60 {
		nextTime = 60
	} else if nextTime > 600 {
		nextTime = 600
	}

	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🎯 849风格智能间隔: 最终使用%d秒", nextTime))
	return nextTime
}

// callSecautoauth 调用HTTP接口进行二次登录，避免循环依赖
func (client *TcpClient) callSecautoauth() models.ResponseResult {
	LogSmartHeartbeat(client.model.Wxid, "📞 开始调用HTTP二次登录接口")

	// 动态获取服务器地址和端口
	httpAddr := beego.AppConfig.String("httpaddr")
	if httpAddr == "" {
		httpAddr = "127.0.0.1" // 默认本地地址
	}

	httpPort := beego.AppConfig.String("httpport")
	if httpPort == "" {
		httpPort = "8080" // 默认端口
	}

	// 构造HTTP请求URL
	baseURL := fmt.Sprintf("http://%s:%s/api/Login/TwiceAutoAuth", httpAddr, httpPort)
	params := url.Values{}
	params.Set("wxid", client.model.Wxid)

	fullURL := baseURL + "?" + params.Encode()
	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🌐 请求URL: %s", fullURL))

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发送POST请求
	resp, err := httpClient.Post(fullURL, "application/x-www-form-urlencoded", nil)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ HTTP请求失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("HTTP请求失败：%v", err.Error()),
			Data:    nil,
		}
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 读取响应失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("读取响应失败：%v", err.Error()),
			Data:    nil,
		}
	}

	// 解析JSON响应
	var result models.ResponseResult
	err = json.Unmarshal(body, &result)
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 解析响应失败: %v", err))
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: fmt.Sprintf("解析响应失败：%v", err.Error()),
			Data:    nil,
		}
	}

	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("✅ HTTP二次登录完成 - Code: %d, Success: %t", result.Code, result.Success))
	return result
}

// handleHeartBeatResponse 处理心跳响应
func (client *TcpClient) handleHeartBeatResponse(messageBody []byte) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("TcpClient: handleHeartBeatResponse panic [%s]: %v", client.model.Wxid, r)
		}
	}()

	// 解包业务数据
	unpackData := Algorithm.UnpackBusinessPacket(messageBody, client.model.Sessionkey, client.model.Uin, &client.model.Cooike)
	if len(unpackData) == 0 {
		log.Errorf("TcpClient: [%s] 心跳响应解包失败", client.model.Wxid)
		// 心跳解包失败，触发重连
		go client.restartConnection()
		return
	}

	// 解析心跳响应
	var heartBeatResp mm.HeartBeatResponse
	err := proto.Unmarshal(unpackData, &heartBeatResp)
	if err != nil {
		log.Errorf("TcpClient: [%s] 心跳响应反序列化失败: %v", client.model.Wxid, err)
		// 心跳解析失败，触发重连
		go client.restartConnection()
		return
	}

	// 检查心跳响应状态
	if heartBeatResp.GetBaseResponse().GetRet() == 0 {
		log.Infof("💚 TcpClient: [%s] 238心跳响应成功！", client.model.Wxid)
		// 更新心跳时间
		client.heartBeatTime = time.Now().Unix()
		log.Infof("⏱️ TcpClient: [%s] 心跳时间已更新: %d", client.model.Wxid, client.heartBeatTime)

		// 849风格的智能间隔调整：根据服务器返回的NextTime动态调整心跳间隔
		nextTime := heartBeatResp.GetNextTime()
		log.Infof("📊 TcpClient: [%s] 238心跳服务返回的NextTime: %d秒", client.model.Wxid, nextTime)

		if nextTime > 0 {
			log.Infof("🎯 TcpClient: [%s] 849风格智能间隔调整: 175秒 -> %d秒", client.model.Wxid, nextTime)
			if client.heartBeatTimer != nil {
				client.heartBeatTimer.Reset(time.Second * time.Duration(nextTime))
			}
		} else {
			log.Infof("⏰ TcpClient: [%s] 服务器未返回有效NextTime，使用默认175秒间隔", client.model.Wxid)
			// 如果服务器没有返回NextTime，使用默认的175秒间隔
			if client.heartBeatTimer != nil {
				client.heartBeatTimer.Reset(time.Second * 175)
			}
		}
	} else {
		log.Errorf("💔 TcpClient: [%s] 238心跳响应失败，返回码: %d，触发重连", client.model.Wxid, heartBeatResp.GetBaseResponse().GetRet())
		// 心跳失败，触发重连
		go client.restartConnection()
	}
}

// restartConnection 849风格的重新连接机制
func (client *TcpClient) restartConnection() {
	LogSmartHeartbeat(client.model.Wxid, "🔄 开始执行849风格重连机制")

	client.Lock()
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("❌ TcpClient: restartConnection panic [%s]: %v", client.model.Wxid, r)
		}
	}()

	// 断开链接
	client.isConnected = false
	LogSmartHeartbeat(client.model.Wxid, "🔌 连接状态设置为false")

	// 发送退出信号
	select {
	case client.exitFlagChan <- true:
		LogSmartHeartbeat(client.model.Wxid, "🚪 退出信号已发送")
	default:
		LogSmartHeartbeat(client.model.Wxid, "⚠️ 退出信号发送失败，通道可能已满")
	}

	// 关闭长链接
	if client.conn != nil {
		LogSmartHeartbeat(client.model.Wxid, "🔌 关闭现有TCP连接")
		client.conn.Close()
	}

	isLock := true
	defer func() {
		if isLock {
			isLock = false
			client.Unlock()
		}
	}()

	go func() {
		// 等待 30 秒超时保护
		LogSmartHeartbeat(client.model.Wxid, "⏳ 启动30秒超时保护goroutine")
		time.Sleep(30 * time.Second)
		if isLock {
			isLock = false
			LogSmartHeartbeat(client.model.Wxid, "⏰ 849风格30秒超时保护触发")
			client.Unlock()
		}
	}()

	// 重新开始连接
	LogSmartHeartbeat(client.model.Wxid, "🔄 开始重新建立连接")
	err := client.reconnect()
	if err != nil {
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("❌ 重连失败: %v", err))
		client.hasError = true
		if isLock {
			isLock = false
			client.Unlock()
		}
		return
	}

	LogSmartHeartbeat(client.model.Wxid, "🎉 849风格重连成功！")

	// 确保重连成功后再释放锁
	if isLock {
		isLock = false
		client.Unlock()
	}

	// 重置心跳定时器，1秒后开始心跳
	if client.heartBeatTimer != nil {
		LogSmartHeartbeat(client.model.Wxid, "⏰ 重置心跳定时器为1秒后触发")
		client.heartBeatTimer.Stop()                 // 先停止
		client.heartBeatTimer.Reset(time.Second * 1) // 再重置
	}
	// 重置二次登录定时器，30-60分钟随机间隔
	if client.autoAuthTimer != nil {
		minutes := uint32(30 + rand.Int31n(30))
		LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🔐 重置二次登录定时器为%d分钟后触发", minutes))
		client.autoAuthTimer.Stop()                                      // 先停止
		client.autoAuthTimer.Reset(time.Minute * time.Duration(minutes)) // 再重置
	}
}

// reconnect 重新连接
func (client *TcpClient) reconnect() error {
	// 更新开始时间戳
	client.startDateTime = time.Now().Unix()
	LogSmartHeartbeat(client.model.Wxid, fmt.Sprintf("🕐 更新startDateTime为: %d", client.startDateTime))

	// 849风格重连：重新建立连接和startLongWriter
	LogSmartHeartbeat(client.model.Wxid, "🔄 开始重新建立TCP连接")

	// 重新建立TCP连接
	var connErr error
	client.conn, connErr = CreateConnection(
		client.model.MarsHost,
		client.model.Proxy.ProxyIp,
		client.model.Proxy.ProxyUser,
		client.model.Proxy.ProxyPassword)
	if connErr != nil {
		if strings.Contains(connErr.Error(), "missing port in address") {
			client.conn, connErr = CreateConnection(
				client.model.MarsHost+":80",
				client.model.Proxy.ProxyIp,
				client.model.Proxy.ProxyUser,
				client.model.Proxy.ProxyPassword)
			if connErr != nil {
				return connErr
			}
		} else {
			return connErr
		}
	}

	// 重置连接状态
	client.hasError = false
	client.stopped = false
	client.readerStopped = false
	client.isConnected = true

	// 重新初始化通道
	if client.longReqQueue == nil {
		client.longReqQueue = make(chan *LongRequest, 5000)
	}
	if client.exitFlagChan == nil {
		client.exitFlagChan = make(chan bool, 1)
	}

	// 重新启动读goroutine
	go client.startReader()

	// 重新启动startLongWriter
	go client.startLongWriter()

	LogSmartHeartbeat(client.model.Wxid, "✅ 849风格重连完成，TCP连接和startLongWriter已重新启动")
	return nil
}

// IsConnected 检查是否连接
func (client *TcpClient) IsConnected() bool {
	client.Lock()
	defer client.Unlock()
	return client.isConnected && !client.hasError
}

// SetHeartBeatTime 设置心跳时间
func (client *TcpClient) SetHeartBeatTime(time int64) {
	client.Lock()
	defer client.Unlock()
	client.heartBeatTime = time
}

// GetHeartBeatTime 获取心跳时间
func (client *TcpClient) GetHeartBeatTime() int64 {
	client.Lock()
	defer client.Unlock()
	return client.heartBeatTime
}

// SendToLongReqQueue 添加到长连接请求队列
func (client *TcpClient) SendToLongReqQueue(longReq *LongRequest) error {
	select {
	case client.longReqQueue <- longReq:
		return nil
	default:
		return errors.New("长连接请求队列已满")
	}
}

// SendHeartBeatWaitingSeconds 849风格的心跳间隔调整
func (client *TcpClient) SendHeartBeatWaitingSeconds(seconds uint32) {
	if client.heartBeatTimer != nil {
		client.heartBeatTimer.Reset(time.Second * time.Duration(seconds))
	}
}

// 849风格：移除了20秒TCP心跳机制，只使用175秒的238应用层智能心跳

// readCompleteMessage 终极零拷贝读取完整协议消息（原版本，保留备用）
func (client *TcpClient) readCompleteMessage() ([]byte, error) {
	// 预分配最小缓冲区用于读取协议头
	headerBuffer := make([]byte, 5)
	_, err := io.ReadFull(client.conn, headerBuffer)
	if err != nil {
		return nil, fmt.Errorf("读取协议头失败: %v", err)
	}

	// 校验报文头的合法性（保持835原始检查逻辑）
	if headerBuffer[1] != CORRECT_HEADER[1] || headerBuffer[2] != CORRECT_HEADER[2] {
		return nil, fmt.Errorf("协议头格式错误: [%x]", headerBuffer)
	}

	// 获取消息体长度
	messageLength := int(binary.BigEndian.Uint16(headerBuffer[3:5]))
	if messageLength < 0 || messageLength > 1024*1024 { // 1MB限制，防止恶意包
		return nil, fmt.Errorf("消息长度异常: %d", messageLength)
	}

	// 终极优化：根据实际需要分配精确大小的内存
	if messageLength == 0 {
		// 只有协议头，直接返回
		return headerBuffer, nil
	}

	// 有消息体时，重新分配完整大小并进行最少的复制
	completeMessage := make([]byte, 5+messageLength)
	copy(completeMessage[:5], headerBuffer) // 只复制一次协议头

	// 直接读取消息体到目标位置（真正的零拷贝）
	_, err = io.ReadFull(client.conn, completeMessage[5:])
	if err != nil {
		return nil, fmt.Errorf("读取消息体失败: %v", err)
	}

	return completeMessage, nil
}

// readCompleteMessageWithStreamSupport 协议优化 + 流式读取支持：解决EOF问题同时保持性能
func (client *TcpClient) readCompleteMessageWithStreamSupport() ([]byte, error) {
	// 第一步：流式读取协议头（5字节），适应TCP分包传输
	headerBuffer := make([]byte, 5)
	bytesRead := 0

	for bytesRead < 5 {
		n, err := client.conn.Read(headerBuffer[bytesRead:])
		if err != nil {
			if err == io.EOF {
				if bytesRead == 0 {
					// 完全没有数据，返回EOF让上层处理（遵循原始代码逻辑）
					return nil, io.EOF
				}
				// 已读取部分数据但遇到EOF，这种情况很少见
				// 按原始代码逻辑：直接返回错误，不等待
				return nil, fmt.Errorf("协议头读取不完整: 已读%d字节", bytesRead)
			}
			return nil, fmt.Errorf("读取协议头失败: %v", err)
		}
		bytesRead += n
	}

	// 第二步：校验协议头（保持原有逻辑）
	if headerBuffer[1] != CORRECT_HEADER[1] || headerBuffer[2] != CORRECT_HEADER[2] {
		return nil, fmt.Errorf("协议头格式错误: [%x]", headerBuffer)
	}

	// 第三步：解析消息长度
	messageLength := int(binary.BigEndian.Uint16(headerBuffer[3:5]))
	if messageLength < 0 || messageLength > 1024*1024 {
		return nil, fmt.Errorf("消息长度异常: %d", messageLength)
	}

	// 第四步：协议优化的精确内存分配
	if messageLength == 0 {
		return headerBuffer, nil
	}

	// 第五步：分配完整消息缓冲区（零拷贝优化）
	completeMessage := make([]byte, 5+messageLength)
	copy(completeMessage[:5], headerBuffer)

	// 第六步：流式读取消息体，适应TCP分包传输
	bytesRead = 0
	for bytesRead < messageLength {
		n, err := client.conn.Read(completeMessage[5+bytesRead:])
		if err != nil {
			if err == io.EOF {
				// 消息体读取中遇到EOF，按原始代码逻辑直接返回错误
				return nil, fmt.Errorf("消息体读取不完整: 需要%d字节，已读%d字节", messageLength, bytesRead)
			}
			return nil, fmt.Errorf("读取消息体失败: %v", err)
		}
		bytesRead += n
	}

	return completeMessage, nil
}

// StartReaderWriter 启动独立的读写goroutine
func (client *TcpClient) StartReaderWriter() {
	if client.conn == nil {
		log.Errorf("TcpClient: 连接为空，无法启动读写goroutine")
		return
	}

	// 启动读goroutine
	go client.startReader()

	log.Infof("TcpClient: 已启动读写goroutine [%s]", client.model.Wxid)
}

// startReader 独立的读goroutine（协议层优化版本）
func (client *TcpClient) startReader() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("TcpClient: 读goroutine panic [%s]: %v", client.model.Wxid, r)
		}
		client.Lock()
		client.readerStopped = true
		client.Unlock()
		log.Infof("TcpClient: 读goroutine已停止 [%s]", client.model.Wxid)
	}()

	log.Infof("TcpClient: 读goroutine已启动 [%s]", client.model.Wxid)

	for {
		select {
		case <-client.stopChan:
			return
		default:
			// 协议优化版本 + 流式读取思想：既保持性能优势，又解决EOF问题
			completeMessage, err := client.readCompleteMessageWithStreamSupport()
			if err != nil {
				if err == io.EOF {
					// EOF是TCP流式传输的正常现象：当前没有数据可读，继续等待
					time.Sleep(10 * time.Millisecond)
					continue
				} else {
					// 其他真正的错误才记录和处理
					log.Errorf("TcpClient: 读取错误 [%s]: %v", client.model.Wxid, err)
					return
				}
			}

			// 直接处理完整消息，保持协议优化的性能优势
			client.ProcessMessage(completeMessage)
		}
	}
}

// ReceiveMessage 识别并预处理消息字节（原始代码逻辑，适应TCP流式传输）
func (client *TcpClient) ReceiveMessage(buf []byte) {
	client.receivedBytes = append(client.receivedBytes, buf...)
	for len(client.receivedBytes) > 5 {
		// 正常的消息报文头为5字节, 小于5字节的报文说明没有接收完成, 不做预处理, 继续接收, 大于5则解析消息头, 逐条取出消息并回调
		// 取出包头, 5字节长度
		headBytes := client.receivedBytes[:5]
		// 校验报文头的合法性
		if headBytes[1] != CORRECT_HEADER[1] || headBytes[2] != CORRECT_HEADER[2] {
			log.Errorf("数据包头格式错误: [%x]", headBytes)
			// 清除缓冲区并退出循环
			client.receivedBytes = []byte{}
			break
		}
		// 消息头3-5为完整消息长度
		messageLen := binary.BigEndian.Uint16(headBytes[3:5])
		if len(client.receivedBytes) < int(messageLen+5) {
			// 数据包长度小于报文长度, 说明还没有接收完成, 退出循环继续接收消息
			log.Infof("TcpClient: ReceiveMessage执行, 消息[%v]完成[%v]", messageLen+5, len(client.receivedBytes))
			break
		}
		// 取出该条消息
		messageBytes := client.receivedBytes[:messageLen+5]
		// 从缓存中移除该条消息
		client.receivedBytes = client.receivedBytes[messageLen+5:]
		// 回调消息处理
		client.ProcessMessage(messageBytes)
	}
}

package TcpPoll

import (
	"errors"
	"sync"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/comm"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

type TcpManager struct {
	running     bool          // 控制是否运行
	connections *sync.Map     // 以关键字为key的连接池 string -> *TcpClient
	stopChan    chan struct{} // 停止通道
}

// TcpManager单例, 使用sync.Once.Do解决并发时多次创建
var once sync.Once
var instance *TcpManager

// 获取单例Tcp
func GetTcpManager() (*TcpManager, error) {
	var err error
	longLinkEnabled, _ := beego.AppConfig.Bool("longlinkenabled")
	if !longLinkEnabled {
		return nil, errors.New("不支持长连接请求")
	}
	once.Do(func() {
		instance = &TcpManager{
			running:     true,
			connections: &sync.Map{},
			stopChan:    make(chan struct{}),
		}
	})
	return instance, err
}

// 队列增加长连接. key: 关键字, client: TcpClient
func (manager *TcpManager) Add(key string, client *TcpClient) error {
	if client.conn == nil {
		return errors.New("TcpManager: 连接为空")
	}

	// 存储连接
	manager.connections.Store(key, client)

	// 启动独立的读写goroutine
	client.StartReaderWriter()

	log.Infof("TcpManager: 已添加连接 [%s]", key)
	return nil
}

// 队列移除长连接. client: TcpClient
func (manager *TcpManager) Remove(client *TcpClient) {
	if client == nil {
		return
	}

	// 停止客户端
	client.Terminate()

	// 从连接池中移除
	manager.connections.Delete(client.model.Wxid)

	log.Infof("TcpManager: 已移除连接 [%s]", client.model.Wxid)
}

// 创建长连接并添加到epoll.
func (manager *TcpManager) GetClient(loginData *comm.LoginData) (*TcpClient, error) {
	// 根据key查找是否存在已有连接, 如果已存在, 则返回
	if clientInterface, ok := manager.connections.Load(loginData.Wxid); ok {
		if client, ok := clientInterface.(*TcpClient); ok && client != nil {
			client.model = loginData
			return client, nil
		}
	}
	// 检查MarsHost
	if loginData.MarsHost == "" {
		loginData.MarsHost = Algorithm.MmtlsLongHost
	}
	// 创建新的连接
	client := NewTcpClient(loginData)
	if err := client.Connect(); err != nil {
		return nil, err
	}
	// 将完成连接的client添加到epoll
	if err := manager.Add(loginData.Wxid, client); err != nil {
		return nil, err
	}
	timeoutSpan, _ := time.ParseDuration(beego.AppConfig.String("longlinkconnecttimeout"))
	timeoutTime := time.Now().Add(timeoutSpan)
	// 进入循环等待, 完成握手或者超时都将退出循环
	for time.Now().Before(timeoutTime) {
		time.Sleep(100 * time.Millisecond)
		// 通过client.handshaking判断是否已经完成握手
		if !client.handshaking {
			break
		}
	}
	if client.handshaking {
		// 超时没有完成握手, 报错
		manager.Remove(client)
		return nil, errors.New("mmtls握手超时")
	}

	return client, nil
}

// RunEventLoop 简化的事件循环 - 现在只是连接管理器
func (manager *TcpManager) RunEventLoop() {
	manager.running = true
	log.Printf("TcpManager: 连接管理器已启动")

	// 定期检查连接状态
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for manager.running {
		select {
		case <-manager.stopChan:
			log.Printf("TcpManager: 收到停止信号，开始优雅关闭")
			manager.running = false
			goto cleanup
		case <-ticker.C:
			// 定期清理错误连接
			manager.cleanupErrorConnections()
		}
	}

cleanup:
	log.Printf("TcpManager: 开始清理资源")

	// 清理所有连接
	manager.connections.Range(func(key, value interface{}) bool {
		if client, ok := value.(*TcpClient); ok && client != nil {
			client.Terminate()
		}
		return true
	})

	log.Printf("TcpManager: 资源清理完成")
}

// cleanupErrorConnections 849风格：基于心跳时间清理连接
func (manager *TcpManager) cleanupErrorConnections() {
	var timeoutClients []*TcpClient
	currentTime := time.Now().Unix()

	// 收集超时连接（849风格：基于心跳时间而不是hasError）
	manager.connections.Range(func(key, value interface{}) bool {
		if client, ok := value.(*TcpClient); ok {
			// 849风格：检查智能心跳机制是否启用
			if client.heartBeatTimer != nil {
				heartBeatTime := client.GetHeartBeatTime()
				// 如果超过5分钟没有心跳更新，认为连接超时
				if heartBeatTime > 0 && currentTime-heartBeatTime > 300 {
					timeoutClients = append(timeoutClients, client)
				}
			} else {
				// 传统方式：没有智能心跳机制时仍使用hasError
				if client.hasError {
					timeoutClients = append(timeoutClients, client)
				}
			}
		}
		return true
	})

	// 清理超时连接
	for _, client := range timeoutClients {
		log.Infof("TcpManager: 849风格清理超时连接[%s]，心跳时间: %d", client.model.Wxid, client.GetHeartBeatTime())
		manager.Remove(client)
	}
}

// 停止TcpManager
func (manager *TcpManager) Stop() {
	if manager.stopChan != nil {
		select {
		case <-manager.stopChan:
			// 已经关闭
		default:
			close(manager.stopChan)
		}
	}
}

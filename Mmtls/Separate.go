package Mmtls

import (
	"encoding/hex"
	"wechatdll/lib"
)

type Separatea struct {
	title  string
	length uint64
	val    []byte
}

// 分包
func Separate(Data []byte) []Separatea {
	var NewData []Separatea
	for {
		// 检查数据长度，确保至少有5字节的头部
		if len(Data) < 5 {
			break
		}

		Len := Data[3:5]
		title := hex.EncodeToString(Data[:1])
		dataLength := int64(lib.Hex2int(&Len))

		// 检查数据长度是否足够包含完整的数据包
		totalLength := 5 + dataLength
		if int64(len(Data)) < totalLength {
			break
		}

		NewData = append(NewData, Separatea{
			title:  title,
			length: lib.Hex2int(&Len),
			val:    Data[5:totalLength],
		})
		Data = Data[totalLength:]
	}
	return NewData
}

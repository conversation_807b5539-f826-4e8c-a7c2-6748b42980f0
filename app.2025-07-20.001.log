2025/07/19 20:08:52.043 [I] [asm_amd64.s:1700]  http server Running on http://:1001
2025/07/19 21:11:55.769 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:11:55.769 [C] [panic.go:787]  <PERSON><PERSON> crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:11:55.769 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:11:55.769 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:46:00.116 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:46:00.116 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:46:00.116 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:46:00.116 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:46:00.117 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:49:42.123 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:49:42.123 [C] [panic.go:787]  Handler crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/19 21:49:42.123 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:49:42.123 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/19 21:49:42.123 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:49:42.123 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:49:42.123 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:49:42.123 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:49:42.124 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:49:42.124 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:49:42.124 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:50:01.354 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:50:01.354 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:50:01.354 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:50:01.354 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:51:33.484 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:51:33.484 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:51:33.484 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:51:33.484 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:52:27.114 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:52:27.114 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:52:27.114 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:52:27.114 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 21:52:51.603 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 21:52:51.603 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 21:52:51.603 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 21:52:51.603 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:35:00.528 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:35:00.528 [C] [panic.go:787]  Handler crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:35:00.528 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:35:00.528 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:35:26.794 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:35:26.794 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:35:26.794 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:35:26.794 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:35:26.795 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:35:26.795 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:35:26.795 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:35:26.795 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:35:26.795 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:35:26.795 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:37:49.195 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:37:49.195 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:37:49.195 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:37:49.195 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:38:33.183 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:38:33.183 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:38:33.183 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:38:33.183 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:39:56.332 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:39:56.332 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:39:56.332 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:39:56.332 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:49:46.083 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:49:46.083 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:49:46.083 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:49:46.083 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:49:46.083 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:49:46.083 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:49:46.083 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:49:46.083 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:49:46.083 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:49:46.083 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:49:46.083 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:49:46.084 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:49:46.084 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:49:46.084 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:49:46.084 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/19 23:53:38.684 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/19 23:53:38.684 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/19 23:53:38.684 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/19 23:53:38.684 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700

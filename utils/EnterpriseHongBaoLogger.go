package utils

import (
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"
)

// EnterpriseHongBaoLogger 企业红包专用日志记录器
type EnterpriseHongBaoLogger struct {
	mu      sync.Mutex
	file    *os.File
	logger  *log.Logger
	logPath string
	enabled bool
}

var (
	globalLogger *EnterpriseHongBaoLogger
	once         sync.Once
)

// GetEnterpriseLogger 获取企业红包专用日志记录器
func GetEnterpriseLogger() *EnterpriseHongBaoLogger {
	once.Do(func() {
		globalLogger = NewEnterpriseHongBaoLogger()
	})
	return globalLogger
}

// NewEnterpriseHongBaoLogger 创建新的企业红包日志记录器
func NewEnterpriseHongBaoLogger() *EnterpriseHongBaoLogger {
	// 创建日志文件，直接在项目根目录
	today := time.Now().Format("2006-01-02")
	logPath := fmt.Sprintf("enterprise_hongbao_%s.log", today)

	file, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Printf("创建企业红包日志文件失败: %v\n", err)
		return &EnterpriseHongBaoLogger{enabled: false}
	}

	logger := log.New(file, "", log.LstdFlags|log.Lmicroseconds)

	hbLogger := &EnterpriseHongBaoLogger{
		file:    file,
		logger:  logger,
		logPath: logPath,
		enabled: true,
	}

	// 记录启动信息
	hbLogger.Info("=== 企业红包日志系统启动 ===")
	hbLogger.Info("日志文件: %s", logPath)

	return hbLogger
}

// Info 记录信息日志
func (l *EnterpriseHongBaoLogger) Info(format string, args ...interface{}) {
	if !l.enabled {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[INFO] %s", message)

	// 同时输出到控制台
	fmt.Printf("[企业红包日志] %s\n", message)
}

// Debug 记录调试日志
func (l *EnterpriseHongBaoLogger) Debug(format string, args ...interface{}) {
	if !l.enabled {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[DEBUG] %s", message)

	// 同时输出到控制台
	fmt.Printf("[企业红包调试] %s\n", message)
}

// Error 记录错误日志
func (l *EnterpriseHongBaoLogger) Error(format string, args ...interface{}) {
	if !l.enabled {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[ERROR] %s", message)

	// 同时输出到控制台
	fmt.Printf("[企业红包错误] %s\n", message)
}

// Success 记录成功日志
func (l *EnterpriseHongBaoLogger) Success(format string, args ...interface{}) {
	if !l.enabled {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[SUCCESS] %s", message)

	// 同时输出到控制台
	fmt.Printf("[企业红包成功] ✅ %s\n", message)
}

// Close 关闭日志文件
func (l *EnterpriseHongBaoLogger) Close() {
	if !l.enabled || l.file == nil {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	l.logger.Printf("[INFO] 企业红包日志系统关闭")
	l.file.Close()
}

// GetLogPath 获取日志文件路径
func (l *EnterpriseHongBaoLogger) GetLogPath() string {
	return l.logPath
}

// IsEnabled 检查日志是否启用
func (l *EnterpriseHongBaoLogger) IsEnabled() bool {
	return l.enabled
}

// 便捷函数
func LogEnterpriseInfo(format string, args ...interface{}) {
	GetEnterpriseLogger().Info(format, args...)
}

func LogEnterpriseDebug(format string, args ...interface{}) {
	GetEnterpriseLogger().Debug(format, args...)
}

func LogEnterpriseError(format string, args ...interface{}) {
	GetEnterpriseLogger().Error(format, args...)
}

func LogEnterpriseSuccess(format string, args ...interface{}) {
	GetEnterpriseLogger().Success(format, args...)
}

func GetEnterpriseLogPath() string {
	return GetEnterpriseLogger().GetLogPath()
}

func CloseEnterpriseLogger() {
	if globalLogger != nil {
		globalLogger.Close()
	}
}

// LogHongBaoDetails 记录红包详细信息
func LogHongBaoDetails(action string, sceneID int, payMsgID, nativeURL string) {
	logger := GetEnterpriseLogger()
	logger.Info("=== %s 红包详细信息 ===", action)
	logger.Info("SceneID: %d (%s)", sceneID, getHongBaoTypeName(sceneID))
	logger.Info("PayMsgID: %s", payMsgID)
	logger.Info("NativeURL: %s", nativeURL)
	logger.Info("========================")
}

// LogRequestDetails 记录请求详细信息
func LogRequestDetails(action string, cgiCmd int, cgiUrl string, cgiType int, requestText string) {
	logger := GetEnterpriseLogger()
	logger.Info("=== %s 请求详细信息 ===", action)
	logger.Info("CgiCmd: %d (0x%x)", cgiCmd, cgiCmd)
	logger.Info("CgiUrl: %s", cgiUrl)
	logger.Info("CgiType: %d", cgiType)
	logger.Info("RequestText: %s", requestText)
	logger.Info("========================")
}

// LogResponseDetails 记录响应详细信息
func LogResponseDetails(action string, success bool, dataLen int, errorMsg string) {
	logger := GetEnterpriseLogger()
	if success {
		logger.Success("%s成功 - 响应数据长度: %d", action, dataLen)
	} else {
		logger.Error("%s失败 - 错误: %s", action, errorMsg)

		// 分析常见错误类型
		if errorMsg != "" {
			logger.Error("错误分析:")
			if strings.Contains(errorMsg, "MM_ERR_SYS") {
				logger.Error("  - MM_ERR_SYS: 微信系统错误，可能原因:")
				logger.Error("    1. 红包已被抢完或过期")
				logger.Error("    2. 账号权限不足")
				logger.Error("    3. 网络延迟或时机问题")
				logger.Error("    4. 企业红包特殊限制")
			} else if strings.Contains(errorMsg, "MM_ERR_TIMEOUT") {
				logger.Error("  - MM_ERR_TIMEOUT: 请求超时")
			} else if strings.Contains(errorMsg, "MM_ERR_NETWORK") {
				logger.Error("  - MM_ERR_NETWORK: 网络错误")
			}
		}
	}
}

// getHongBaoTypeName 获取红包类型名称
func getHongBaoTypeName(sceneID int) string {
	switch sceneID {
	case 1002:
		return "普通红包"
	case 1005:
		return "企业红包"
	default:
		return "未知类型"
	}
}

// TestLogger 测试日志功能
func TestLogger() {
	logger := GetEnterpriseLogger()
	logger.Info("测试企业红包日志系统")
	logger.Debug("这是一条调试信息")
	logger.Success("这是一条成功信息")
	logger.Error("这是一条错误信息")

	// 测试详细日志
	LogHongBaoDetails("测试", 1005, "test123456", "wxpay://test")
	LogRequestDetails("测试", 5148, "/cgi-bin/mmpay-bin/receiveunionhb", 1582, "test=123")
	LogResponseDetails("测试", true, 256, "")

	logger.Info("企业红包日志系统测试完成")
}

2025/07/20 03:02:23.592 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 03:02:23.592 [C] [panic.go:787]  <PERSON><PERSON> crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/20 03:02:23.592 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 03:02:23.592 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/20 03:02:23.592 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/20 03:02:23.592 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/20 03:02:23.592 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/20 03:02:23.592 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/20 03:02:23.592 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/20 03:02:23.592 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 03:02:23.592 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 03:02:23.592 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 03:02:23.593 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 03:02:23.593 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 03:02:23.593 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 03:02:23.593 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 03:04:53.566 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 03:04:53.566 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 03:04:53.566 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 03:04:53.566 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 03:12:39.091 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 03:12:39.091 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 03:12:39.091 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 03:12:39.091 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 03:12:39.091 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 03:12:39.092 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 03:12:39.092 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 03:12:39.092 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 03:12:39.092 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 03:12:39.092 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 03:12:39.092 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 03:12:39.092 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 03:12:39.092 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 03:12:39.092 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 03:12:39.092 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 03:13:51.546 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 03:13:51.546 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 03:13:51.546 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 03:13:51.546 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 03:20:36.452 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 03:20:36.452 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 03:20:36.452 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 03:20:36.452 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 03:20:36.452 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 03:20:36.453 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 03:20:36.453 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 03:20:36.453 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 03:20:36.453 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 03:20:36.453 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 03:20:36.453 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 03:20:36.453 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 03:20:36.453 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 03:20:36.453 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 03:20:36.453 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 03:34:09.255 [I] [asm_amd64.s:1700]  http server Running on http://:1001
2025/07/20 05:06:38.846 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 05:06:38.846 [C] [panic.go:787]  Handler crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 05:06:38.846 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 05:06:38.846 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 08:43:20.642 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 08:43:20.642 [C] [panic.go:787]  Handler crashed with error reflect: call of reflect.Value.FieldByName on zero Value
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:218
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:1346
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:90
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/EpollLinux.go:51
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpManager.go:68
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:153
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 08:43:20.642 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 08:43:20.642 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 08:43:20.643 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 08:44:24.987 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 08:44:24.987 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 08:44:24.987 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 08:44:24.987 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 08:44:24.987 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 08:44:24.987 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 08:44:24.987 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 08:44:24.988 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 08:44:24.988 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 08:44:24.988 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 08:44:24.988 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 08:44:24.988 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 08:44:24.988 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 08:44:24.988 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 08:44:24.988 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 08:45:27.708 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 08:45:27.708 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 08:45:27.709 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 08:45:27.709 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 08:46:27.035 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 08:46:27.036 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 08:46:27.036 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 08:46:27.036 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 10:31:54.742 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 10:31:54.743 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 10:31:54.743 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 10:31:54.743 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 10:43:32.706 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 10:43:32.706 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 10:43:32.706 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 10:43:32.706 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 10:50:30.307 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 10:50:30.307 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 10:50:30.307 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 10:50:30.307 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 10:50:30.307 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 10:50:30.307 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 10:50:30.307 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 10:50:30.307 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 10:50:30.307 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 10:50:30.307 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 10:50:30.307 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 10:50:30.308 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 10:50:30.308 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 10:50:30.308 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 10:50:30.308 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 10:51:26.785 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 10:51:26.785 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 10:51:26.785 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 10:51:26.785 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700
2025/07/20 13:05:09.762 [C] [panic.go:787]  the request url is  /api/Login/HeartBeatLong
2025/07/20 13:05:09.762 [C] [panic.go:787]  Handler crashed with error runtime error: slice bounds out of range [:12] with capacity 0
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:787
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/runtime/panic.go:141
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/Mmtls/Util.go:126
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:467
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/TcpPoll/TcpClient.go:604
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/models/Login/HeartBeat.go:151
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/Desktop/go835dm0106_07mac_副本修复唤醒ipad/controllers/Login.go:413
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:581
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/reflect/value.go:365
2025/07/20 13:05:09.762 [C] [panic.go:787]  /Users/<USER>/go/pkg/mod/github.com/astaxie/beego@v1.12.3/router.go:897
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:3301
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/net/http/server.go:2102
2025/07/20 13:05:09.762 [C] [panic.go:787]  /usr/local/go/src/runtime/asm_amd64.s:1700

package main

import (
	"encoding/xml"
	"fmt"
	"wechatdll/Xml"
)

// 测试企业红包支持
func main() {
	fmt.Println("=== 企业红包支持测试 ===")

	// 模拟普通红包XML
	normalHongBaoXML := `<msg>
		<appmsg>
			<wcpayinfo>
				<templateid><![CDATA[]]></templateid>
				<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039501202412201234567890123456&sendusername=test_user]]></nativeurl>
				<sceneid><![CDATA[1002]]></sceneid>
				<paymsgid><![CDATA[1000039501202412201234567890123456]]></paymsgid>
				<invalidtime>1734681600</invalidtime>
			</wcpayinfo>
		</appmsg>
	</msg>`

	// 模拟企业红包XML
	enterpriseHongBaoXML := `<msg>
		<appmsg>
			<wcpayinfo>
				<templateid><![CDATA[]]></templateid>
				<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039501202412201234567890123456&sendusername=test_user]]></nativeurl>
				<sceneid><![CDATA[1005]]></sceneid>
				<paymsgid><![CDATA[1000039501202412201234567890123456]]></paymsgid>
				<invalidtime>1734681600</invalidtime>
			</wcpayinfo>
		</appmsg>
	</msg>`

	// 测试普通红包解析
	fmt.Println("\n1. 测试普通红包解析:")
	testHongBaoType(normalHongBaoXML, "普通红包")

	// 测试企业红包解析
	fmt.Println("\n2. 测试企业红包解析:")
	testHongBaoType(enterpriseHongBaoXML, "企业红包")

	// 测试接收红包CgiCmd选择
	fmt.Println("\n3. 测试接收红包CgiCmd选择:")
	testReceiveCgiCmd(normalHongBaoXML, "普通红包")
	testReceiveCgiCmd(enterpriseHongBaoXML, "企业红包")

	// 测试打开红包CgiCmd选择
	fmt.Println("\n4. 测试打开红包CgiCmd选择:")
	testOpenCgiCmd(normalHongBaoXML, "普通红包")
	testOpenCgiCmd(enterpriseHongBaoXML, "企业红包")

	// 测试URL选择
	fmt.Println("\n5. 测试URL和Cgi类型选择:")
	testUrlSelection(normalHongBaoXML, "普通红包")
	testUrlSelection(enterpriseHongBaoXML, "企业红包")

	// 显示总结
	fmt.Println("\n=== 企业红包支持总结 ===")
	fmt.Println("✅ 当前项目已完整支持企业红包:")
	fmt.Println("   - XML解析: 支持SceneID字段识别企业红包")
	fmt.Println("   - 接收红包: 企业红包使用CgiCmd 0x141c (5148)")
	fmt.Println("   - 打开红包: 企业红包使用CgiCmd 5148")
	fmt.Println("   - 接收URL: 企业红包使用 /cgi-bin/mmpay-bin/receiveunionhb")
	fmt.Println("   - 打开URL: 企业红包使用 /cgi-bin/mmpay-bin/openunionhb")
	fmt.Println("   - 枚举常量: 已添加企业红包相关常量")
	fmt.Println("   - 数据结构: 已支持SceneID字段")

	fmt.Println("\n=== 测试完成 ===")
}

func testHongBaoType(xmlContent, expectedType string) {
	var hongBao Xml.HongBao
	err := xml.Unmarshal([]byte(xmlContent), &hongBao)
	if err != nil {
		fmt.Printf("❌ %s XML解析失败: %v\n", expectedType, err)
		return
	}

	fmt.Printf("✅ %s XML解析成功\n", expectedType)
	fmt.Printf("   SceneID: %d\n", hongBao.Appmsg.Wcpayinfo.Sceneid)
	fmt.Printf("   NativeURL: %s\n", hongBao.Appmsg.Wcpayinfo.Nativeurl)
	fmt.Printf("   PayMsgID: %s\n", hongBao.Appmsg.Wcpayinfo.Paymsgid)
}

func testReceiveCgiCmd(xmlContent, hongBaoType string) {
	var hongBao Xml.HongBao
	err := xml.Unmarshal([]byte(xmlContent), &hongBao)
	if err != nil {
		fmt.Printf("❌ %s 接收测试失败: XML解析错误\n", hongBaoType)
		return
	}

	// 模拟接收红包的CgiCmd选择逻辑
	cgiCmd := 3 // 默认普通红包
	if hongBao.Appmsg.Wcpayinfo.Sceneid == 1005 {
		cgiCmd = 0x141c // 企业红包使用特殊的CgiCmd
	}

	expectedCmd := 3
	if hongBaoType == "企业红包" {
		expectedCmd = 0x141c
	}

	if cgiCmd == expectedCmd {
		fmt.Printf("✅ %s 接收CgiCmd选择正确: %d (0x%x)\n", hongBaoType, cgiCmd, cgiCmd)
	} else {
		fmt.Printf("❌ %s 接收CgiCmd选择错误: 期望 %d, 实际 %d\n", hongBaoType, expectedCmd, cgiCmd)
	}
}

func testOpenCgiCmd(xmlContent, hongBaoType string) {
	var hongBao Xml.HongBao
	err := xml.Unmarshal([]byte(xmlContent), &hongBao)
	if err != nil {
		fmt.Printf("❌ %s 打开测试失败: XML解析错误\n", hongBaoType)
		return
	}

	// 模拟打开红包的CgiCmd选择逻辑
	cgiCmd := 4 // 默认普通红包
	if hongBao.Appmsg.Wcpayinfo.Sceneid == 1005 {
		cgiCmd = 5148 // 企业红包使用特殊的CgiCmd
	}

	expectedCmd := 4
	if hongBaoType == "企业红包" {
		expectedCmd = 5148
	}

	if cgiCmd == expectedCmd {
		fmt.Printf("✅ %s 打开CgiCmd选择正确: %d\n", hongBaoType, cgiCmd)
	} else {
		fmt.Printf("❌ %s 打开CgiCmd选择错误: 期望 %d, 实际 %d\n", hongBaoType, expectedCmd, cgiCmd)
	}
}

func testUrlSelection(xmlContent, hongBaoType string) {
	var hongBao Xml.HongBao
	err := xml.Unmarshal([]byte(xmlContent), &hongBao)
	if err != nil {
		fmt.Printf("❌ %s URL测试失败: XML解析错误\n", hongBaoType)
		return
	}

	// 模拟接收红包的URL选择逻辑
	receiveUrl := "/cgi-bin/mmpay-bin/receivewxhb"
	receiveCgiType := 1581
	if hongBao.Appmsg.Wcpayinfo.Sceneid == 1005 {
		receiveUrl = "/cgi-bin/mmpay-bin/receiveunionhb"
		receiveCgiType = 1582
	}

	// 模拟打开红包的URL选择逻辑
	openUrl := "/cgi-bin/mmpay-bin/openwxhb"
	openCgiType := 1685
	if hongBao.Appmsg.Wcpayinfo.Sceneid == 1005 {
		openUrl = "/cgi-bin/mmpay-bin/openunionhb"
		openCgiType = 1686
	}

	expectedReceiveUrl := "/cgi-bin/mmpay-bin/receivewxhb"
	expectedOpenUrl := "/cgi-bin/mmpay-bin/openwxhb"
	expectedReceiveCgiType := 1581
	expectedOpenCgiType := 1685

	if hongBaoType == "企业红包" {
		expectedReceiveUrl = "/cgi-bin/mmpay-bin/receiveunionhb"
		expectedOpenUrl = "/cgi-bin/mmpay-bin/openunionhb"
		expectedReceiveCgiType = 1582
		expectedOpenCgiType = 1686
	}

	// 验证接收URL
	if receiveUrl == expectedReceiveUrl && receiveCgiType == expectedReceiveCgiType {
		fmt.Printf("✅ %s 接收URL选择正确: %s (Cgi: %d)\n", hongBaoType, receiveUrl, receiveCgiType)
	} else {
		fmt.Printf("❌ %s 接收URL选择错误\n", hongBaoType)
	}

	// 验证打开URL
	if openUrl == expectedOpenUrl && openCgiType == expectedOpenCgiType {
		fmt.Printf("✅ %s 打开URL选择正确: %s (Cgi: %d)\n", hongBaoType, openUrl, openCgiType)
	} else {
		fmt.Printf("❌ %s 打开URL选择错误\n", hongBaoType)
	}
}
